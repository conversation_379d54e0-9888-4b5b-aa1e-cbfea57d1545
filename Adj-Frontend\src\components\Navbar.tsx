import { useEffect, useState, type ReactNode } from 'react';
import { Link, useNavigate, useLocation } from 'react-router-dom';
import { motion, AnimatePresence } from 'framer-motion';
import { ChevronDownIcon, MenuIcon, XIcon } from 'lucide-react';
const Navbar = () => {
  const navigate = useNavigate();
  const location = useLocation();
  const [isScrolled, setIsScrolled] = useState(false);
  const [mobileMenuOpen, setMobileMenuOpen] = useState(false);
  const [servicesDropdownOpen, setServicesDropdownOpen] = useState(false);
  
  // Check if we're on a car detail page
  const isCarDetailPage = location.pathname.startsWith('/cars/') && location.pathname !== '/cars';
  useEffect(() => {
    const handleScroll = () => {
      if (window.scrollY > 50) {
        setIsScrolled(true);
      } else {
        setIsScrolled(false);
      }
    };
    window.addEventListener('scroll', handleScroll);
    return () => {
      window.removeEventListener('scroll', handleScroll);
    };
  }, []);
  const services = [{
    name: 'Transmission Rebuilding',
    link: '/services#transmission'
  }, {
    name: 'Engine Repair',
    link: '/services#engine'
  }, {
    name: 'Advanced Diagnostics',
    link: '/services#diagnostics'
  }, {
    name: 'Other Services',
    link: '/services#other'
  }];
  const navbarVariants = {
    initial: {
      backgroundColor: isCarDetailPage ? 'rgba(30, 58, 95, 0.95)' : 'rgba(30, 58, 95, 0)',
      boxShadow: isCarDetailPage ? '0 10px 30px -10px rgba(0,0,0,0.3)' : 'none'
    },
    scrolled: {
      backgroundColor: 'rgba(30, 58, 95, 0.95)',
      boxShadow: '0 10px 30px -10px rgba(0,0,0,0.3)'
    }
  };
  const dropdownVariants = {
    hidden: {
      opacity: 0,
      y: -20,
      height: 0
    },
    visible: {
      opacity: 1,
      y: 0,
      height: 'auto',
      transition: {
        duration: 0.3,
        staggerChildren: 0.05
      }
    },
    exit: {
      opacity: 0,
      y: -20,
      height: 0,
      transition: {
        duration: 0.2
      }
    }
  };
  const itemVariants = {
    hidden: {
      opacity: 0,
      y: -10
    },
    visible: {
      opacity: 1,
      y: 0
    }
  };
  const mobileMenuVariants = {
    closed: {
      opacity: 0,
      x: '100%',
      transition: {
        type: 'spring',
        stiffness: 400,
        damping: 40
      }
    },
    open: {
      opacity: 1,
      x: '0%',
      transition: {
        type: 'spring',
        stiffness: 400,
        damping: 40
      }
    }
  };
  const handleBookService = () => {
    navigate('/book-service');
  };
  return <motion.nav className={`fixed w-full z-50 px-4 md:px-8 h-16 md:h-20 transition-all duration-300`} variants={navbarVariants} animate={isScrolled || isCarDetailPage ? 'scrolled' : 'initial'} initial="initial">
      <div className="max-w-7xl mx-auto h-full flex justify-between items-center">
        <Link to="/">
          <motion.div className="flex items-center" whileHover={{
          scale: 1.05
        }} whileTap={{
          scale: 0.95
        }}>
            <img
              src="/images/logos/adj-text-logo.svg"
              alt="ADJ Automotive"
              className="h-[150px] w-auto"
              loading="eager"
            />
          </motion.div>
        </Link>
        {/* Desktop Navigation */}
        <div className="hidden md:flex items-center space-x-8">
          <NavLink to="/">Home</NavLink>
          <div className="relative" onMouseEnter={() => setServicesDropdownOpen(true)} onMouseLeave={() => setServicesDropdownOpen(false)}>
            <Link to="/services" className="text-white hover:text-blue-200 font-medium flex items-center">
              Services <ChevronDownIcon className="ml-1 h-4 w-4" />
            </Link>
            <AnimatePresence>
              {servicesDropdownOpen && <motion.div className="absolute left-0 mt-2 w-64 rounded-md shadow-lg bg-white py-1 z-50" variants={dropdownVariants} initial="hidden" animate="visible" exit="exit">
                  {services.map((service, index) => <motion.div key={index} variants={itemVariants}>
                      <Link to={service.link} className="block px-4 py-3 text-sm text-gray-800 hover:bg-blue-50 hover:text-[#1e3a5f] transition-colors" onClick={() => setServicesDropdownOpen(false)}>
                        {service.name}
                      </Link>
                    </motion.div>)}
                </motion.div>}
            </AnimatePresence>
          </div>
          <NavLink to="/cars">Cars for Sale</NavLink>
          <NavLink to="/about">About Us</NavLink>
          <NavLink to="/contact">Contact</NavLink>
          <motion.button className="bg-white text-[#1e3a5f] px-5 py-2 rounded-full font-semibold" whileHover={{
          scale: 1.05,
          backgroundColor: '#f8f9fa'
        }} whileTap={{
          scale: 0.95
        }} onClick={handleBookService}>
            Book Service
          </motion.button>
        </div>
        {/* Mobile menu button */}
        <div className="md:hidden">
          <button className="text-white" onClick={() => setMobileMenuOpen(!mobileMenuOpen)}>
            {mobileMenuOpen ? <XIcon className="h-8 w-8" /> : <MenuIcon className="h-8 w-8" />}
          </button>
        </div>
      </div>
      {/* Mobile Navigation */}
      <AnimatePresence>
        {mobileMenuOpen && <motion.div className="fixed inset-0 bg-[#1e3a5f] z-40 md:hidden" variants={mobileMenuVariants} initial="closed" animate="open" exit="closed">
            {/* Close button */}
            <div className="flex justify-between items-center p-4 pt-6">
              <img
                src="/images/logos/adj-text-logo.svg"
                alt="ADJ Automotive"
                className="h-[150px] w-auto"
                loading="eager"
              />
              <button 
                className="text-white p-2" 
                onClick={() => setMobileMenuOpen(false)}
              >
                <XIcon className="h-8 w-8" />
              </button>
            </div>
            <div className="flex flex-col space-y-6 px-6 pt-4">
              <MobileNavLink to="/" onClick={() => setMobileMenuOpen(false)}>
                Home
              </MobileNavLink>
              <div className="border-b border-blue-400/30 pb-4">
                <div className="flex items-center justify-between mb-4">
                  <Link to="/services" className="text-white text-xl font-medium" onClick={() => setMobileMenuOpen(false)}>
                    Services
                  </Link>
                  <button className="text-white" onClick={() => setServicesDropdownOpen(!servicesDropdownOpen)}>
                    <ChevronDownIcon className={`h-6 w-6 transition-transform ${servicesDropdownOpen ? 'rotate-180' : ''}`} />
                  </button>
                </div>
                <AnimatePresence>
                  {servicesDropdownOpen && <motion.div className="pl-4 space-y-3" variants={dropdownVariants} initial="hidden" animate="visible" exit="exit">
                      {services.map((service, index) => <motion.div key={index} variants={itemVariants}>
                          <Link to={service.link} className="block text-blue-100 hover:text-white text-lg" onClick={() => setMobileMenuOpen(false)}>
                            {service.name}
                          </Link>
                        </motion.div>)}
                    </motion.div>}
                </AnimatePresence>
              </div>
              <MobileNavLink to="/cars" onClick={() => setMobileMenuOpen(false)}>
                Cars for Sale
              </MobileNavLink>
              <MobileNavLink to="/about" onClick={() => setMobileMenuOpen(false)}>
                About Us
              </MobileNavLink>
              <MobileNavLink to="/contact" onClick={() => setMobileMenuOpen(false)}>
                Contact
              </MobileNavLink>
              <motion.button className="bg-white text-[#1e3a5f] py-3 rounded-full font-bold text-lg mt-4" whileHover={{
            scale: 1.02
          }} whileTap={{
            scale: 0.98
          }} onClick={() => {
            setMobileMenuOpen(false);
            navigate('/book-service');
          }}>
                Book Service
              </motion.button>
            </div>
          </motion.div>}
      </AnimatePresence>
    </motion.nav>;
};
const NavLink = ({ to, children }: { to: string; children: ReactNode }) => (
  <Link to={to} className="text-white hover:text-blue-200 font-medium">
    {children}
  </Link>
);

const MobileNavLink = ({
  to,
  children,
  onClick
}: {
  to: string;
  children: ReactNode;
  onClick?: () => void;
}) => (
  <Link
    to={to}
    className="text-white text-xl font-medium border-b border-blue-400/30 pb-4"
    onClick={onClick}
  >
    {children}
  </Link>
);
export default Navbar;