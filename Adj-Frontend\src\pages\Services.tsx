import { useEffect, useRef } from 'react';
import { motion, useAnimation, useInView } from 'framer-motion';
import { Link } from 'react-router-dom';
import ServiceForm from '../components/ServiceForm';
import { CheckIcon, ArrowRightIcon, WrenchIcon, GaugeIcon, ActivityIcon, BatteryChargingIcon, ThermometerIcon, KeyIcon } from 'lucide-react';
const Services = () => {
  return <main className="w-full">
      <ServiceHero />
      <ServiceDetails />
      <ServicePricing />
      <ServiceCertifications />
      <ServiceBooking />
    </main>;
};
const ServiceHero = () => {
  const containerVariants = {
    hidden: {
      opacity: 0
    },
    visible: {
      opacity: 1,
      transition: {
        staggerChildren: 0.2
      }
    }
  };
  const itemVariants = {
    hidden: {
      y: 30,
      opacity: 0
    },
    visible: {
      y: 0,
      opacity: 1,
      transition: {
        duration: 0.6,
        ease: 'easeOut'
      }
    }
  };
  return <section className="relative py-20 bg-[#1e3a5f] overflow-hidden">
      <div className="absolute inset-0 z-0">
        <div className="absolute inset-0 bg-black/50 z-10" style={{
        backgroundImage: 'linear-gradient(to bottom, rgba(30,58,95,0.8), rgba(15,37,66,0.95))'
      }}></div>
        <div className="w-full h-full bg-cover bg-center" style={{
        backgroundImage: "url('https://images.unsplash.com/photo-1619642751034-765dfdf7c58e?ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D&auto=format&fit=crop&w=1335&q=80')"
      }}></div>
      </div>
      <div className="container mx-auto px-4 md:px-8 relative z-10">
        <motion.div className="max-w-3xl mx-auto text-center" variants={containerVariants} initial="hidden" animate="visible">
          <motion.span variants={itemVariants} className="inline-block bg-blue-700 text-white px-4 py-1 rounded-full text-sm font-semibold mb-3">
            OUR SERVICES
          </motion.span>
          <motion.h1 variants={itemVariants} className="text-3xl sm:text-4xl md:text-5xl lg:text-6xl font-bold text-white mb-4 md:mb-6 leading-tight">
            Expert Automotive Services for All Makes & Models
          </motion.h1>
          <motion.p variants={itemVariants} className="text-base sm:text-lg md:text-xl text-blue-100 mb-6 md:mb-8 leading-relaxed">
            From transmission rebuilding to advanced diagnostics, our ASE
            certified technicians provide dealership-quality repairs at
            affordable prices.
          </motion.p>
          <motion.div variants={itemVariants} className="flex flex-col sm:flex-row justify-center gap-3 sm:gap-4">
            <Link to="#booking" className="bg-blue-600 hover:bg-blue-700 text-white px-6 sm:px-8 py-3 rounded-full font-semibold text-base sm:text-lg flex items-center justify-center w-full sm:w-auto">
              Book Appointment <ArrowRightIcon className="ml-2 h-4 w-4 sm:h-5 sm:w-5" />
            </Link>
            <a href="tel:+16714838335" className="bg-white hover:bg-gray-100 text-[#1e3a5f] px-6 sm:px-8 py-3 rounded-full font-semibold text-base sm:text-lg w-full sm:w-auto text-center">
              Call Us Now
            </a>
          </motion.div>
        </motion.div>
      </div>
    </section>;
};
const ServiceDetails = () => {
  const services = [{
    id: 'transmission',
    icon: <WrenchIcon className="h-10 w-10 text-blue-600" />,
    title: 'Transmission Rebuilding',
    description: 'Make your old transmission like brand new with our expert rebuilding service. We specialize in all years, makes, and models with a 1-year labor warranty included.',
    features: ['Complete transmission overhaul', 'FREE promotional items included', 'Same warranty as dealer replacement', 'Removal and installation included', 'All fluids and cleaners included'],
    image: 'https://images.unsplash.com/photo-1619642751034-765dfdf7c58e?ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D&auto=format&fit=crop&w=1335&q=80'
  }, {
    id: 'engine',
    icon: <div className="h-10 w-10 text-blue-600" />,
    title: 'Engine Repair & Rebuilding',
    description: "Our master certified technicians provide complete engine overhauls and repairs to restore your vehicle's performance and reliability.",
    features: ['Complete engine overhauls', 'Professional grade service', 'Performance optimization', 'Comprehensive testing', 'Factory-trained technicians'],
    image: 'https://images.unsplash.com/photo-1486262715619-67b85e0b08d3?ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D&auto=format&fit=crop&w=1335&q=80'
  }, {
    id: 'diagnostics',
    icon: <GaugeIcon className="h-10 w-10 text-blue-600" />,
    title: 'Advanced Diagnostics',
    description: 'Using the latest Autel MaxiSys Ultra scan tool for accurate diagnosis of even the most complex vehicle issues and control system programming.',
    features: ['Latest diagnostic equipment', 'System computer module programming', 'ALLData Repair database access', 'Advanced vehicle control systems', 'Complex electrical troubleshooting'],
    image: 'https://images.unsplash.com/photo-1601362840469-51e4d8d58785?ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D&auto=format&fit=crop&w=1335&q=80'
  }];
  const additionalServices = [{
    icon: <ActivityIcon />,
    title: 'Brakes',
    link: '#brakes'
  }, {
    icon: <div />,
    title: 'Suspension',
    link: '#suspension'
  }, {
    icon: <ThermometerIcon />,
    title: 'Heating & A/C',
    link: '#hvac'
  }, {
    icon: <BatteryChargingIcon />,
    title: 'Electrical',
    link: '#electrical'
  }, {
    icon: <div />,
    title: 'Exhaust',
    link: '#exhaust'
  }, {
    icon: <KeyIcon />,
    title: 'Key Programming',
    link: '#keys'
  }, {
    icon: <div />,
    title: 'General Repair',
    link: '#general'
  }];
  return <section className="py-12 sm:py-16 md:py-20 bg-white">
      <div className="container mx-auto px-4 md:px-8">
        <div className="text-center mb-16">
          <span className="inline-block bg-blue-100 text-blue-800 px-4 py-1 rounded-full text-sm font-semibold mb-3">
            WHAT WE OFFER
          </span>
          <h2 className="text-4xl font-bold text-gray-900 mb-4">
            Our Specialized Services
          </h2>
          <p className="text-xl text-gray-600 max-w-3xl mx-auto">
            We provide a comprehensive range of automotive services with a focus
            on transmission rebuilding, engine repair, and advanced diagnostics.
          </p>
        </div>
        <div className="space-y-24">
          {services.map((service, index) => <ServiceDetailItem key={index} service={service} index={index} />)}
        </div>
        <div id="other" className="mt-24 text-center scroll-mt-24">
          <h3 className="text-2xl font-bold text-gray-900 mb-8">
            Additional Services
          </h3>
          <div className="grid grid-cols-2 sm:grid-cols-3 md:grid-cols-4 lg:grid-cols-4 xl:grid-cols-7 gap-3 sm:gap-4 md:gap-5">
            {additionalServices.map((service, index) => <Link key={index} to={service.link} className="flex flex-col items-center p-3 sm:p-4 bg-gray-50 hover:bg-blue-50 rounded-xl transition-colors">
                <div className="bg-[#1e3a5f]/10 p-2 sm:p-3 rounded-full mb-2 sm:mb-3">
                  {service.icon}
                </div>
                <span className="text-gray-800 font-medium text-center text-sm sm:text-base">
                  {service.title}
                </span>
              </Link>)}
          </div>
        </div>
      </div>
    </section>;
};
const ServiceDetailItem = ({
  service,
  index
}) => {
  const ref = useRef(null);
  const isInView = useInView(ref, {
    once: true,
    margin: '-100px'
  });
  const controls = useAnimation();
  useEffect(() => {
    if (isInView) {
      controls.start('visible');
    }
  }, [isInView, controls]);
  const containerVariants = {
    hidden: {
      opacity: 0
    },
    visible: {
      opacity: 1,
      transition: {
        staggerChildren: 0.15,
        delayChildren: 0.2
      }
    }
  };
  const itemVariants = {
    hidden: {
      y: 30,
      opacity: 0
    },
    visible: {
      y: 0,
      opacity: 1,
      transition: {
        duration: 0.5,
        ease: 'easeOut'
      }
    }
  };
  const imageVariants = {
    hidden: {
      x: index % 2 === 0 ? 50 : -50,
      opacity: 0
    },
    visible: {
      x: 0,
      opacity: 1,
      transition: {
        duration: 0.7,
        ease: 'easeOut'
      }
    }
  };
  return <div id={service.id} ref={ref} className="scroll-mt-24">
      <div className={`flex flex-col ${index % 2 === 0 ? 'lg:flex-row' : 'lg:flex-row-reverse'} gap-6 sm:gap-8 lg:gap-12 items-center`}>
        <motion.div className="lg:w-1/2" variants={containerVariants} initial="hidden" animate={controls}>
          <motion.div variants={itemVariants} className="mb-4 flex items-center">
            <div className="bg-blue-100 p-3 rounded-xl mr-4">
              {service.icon}
            </div>
            <h3 className="text-2xl sm:text-3xl font-bold text-gray-900">
              {service.title}
            </h3>
          </motion.div>
          <motion.p variants={itemVariants} className="text-base sm:text-lg text-gray-600 mb-4 sm:mb-6 leading-relaxed">
            {service.description}
          </motion.p>
          <motion.div variants={itemVariants} className="space-y-3 mb-8">
            {service.features.map((feature, i) => <div key={i} className="flex items-start">
                <CheckIcon className="h-5 w-5 text-green-600 mr-3 mt-1 flex-shrink-0" />
                <span className="text-gray-700">{feature}</span>
              </div>)}
          </motion.div>
          <motion.div variants={itemVariants}>
            <Link to="#booking" className="inline-flex items-center bg-[#1e3a5f] hover:bg-blue-800 text-white font-semibold py-3 px-6 rounded-lg transition-colors">
              Book This Service <ArrowRightIcon className="ml-2 h-4 w-4" />
            </Link>
          </motion.div>
        </motion.div>
        <motion.div className="lg:w-1/2" variants={imageVariants} initial="hidden" animate={controls}>
          <div className="rounded-2xl overflow-hidden shadow-xl">
            <img src={service.image} alt={service.title} className="w-full h-auto" />
          </div>
        </motion.div>
      </div>
    </div>;
};
const ServicePricing = () => {
  const ref = useRef(null);
  const isInView = useInView(ref, {
    once: true,
    margin: '-100px'
  });
  const controls = useAnimation();
  useEffect(() => {
    if (isInView) {
      controls.start('visible');
    }
  }, [isInView, controls]);
  const containerVariants = {
    hidden: {
      opacity: 0
    },
    visible: {
      opacity: 1,
      transition: {
        staggerChildren: 0.15,
        delayChildren: 0.2
      }
    }
  };
  const itemVariants = {
    hidden: {
      y: 30,
      opacity: 0
    },
    visible: {
      y: 0,
      opacity: 1,
      transition: {
        duration: 0.5,
        ease: 'easeOut'
      }
    }
  };
  const pricing = [{
    service: 'Car and Light Vehicles',
    rate: '$125 per hour'
  }, {
    service: 'Diesel and Heavy Duty Vehicles',
    rate: '$200 per hour'
  }, {
    service: 'Engine Diagnostics',
    rate: '$150 per hour'
  }, {
    service: 'Transmission Repair',
    rate: '$200 per hour'
  }, {
    service: 'Programming (per module)',
    rate: '$150'
  }, {
    service: 'Diagnostics & Troubleshooting (Car & Light)',
    rate: '$150'
  }, {
    service: 'Diagnostics & Troubleshooting (Diesel & Heavy Trucks)',
    rate: '$250'
  }, {
    service: 'Key Duplicating with Programming',
    rate: 'Starting at $350'
  }, {
    service: 'Transmission Rebuilding',
    rate: '$3,700+'
  }];
  return <section ref={ref} className="py-12 sm:py-16 md:py-20 bg-slate-100">
      <div className="container mx-auto px-4 md:px-8">
        <motion.div className="max-w-4xl mx-auto" variants={containerVariants} initial="hidden" animate={controls}>
          <motion.div variants={itemVariants} className="text-center mb-12">
            <span className="inline-block bg-blue-100 text-blue-800 px-4 py-1 rounded-full text-sm font-semibold mb-3">
              PRICING
            </span>
            <h2 className="text-4xl font-bold text-gray-900 mb-4">
              Service Rates
            </h2>
            <p className="text-xl text-gray-600">
              Transparent pricing for all our automotive services. Note that
              prices do not include parts.
            </p>
          </motion.div>
          {/* Mobile Card Layout */}
          <motion.div variants={itemVariants} className="block md:hidden">
            <div className="space-y-4">
              {pricing.map((item, index) => (
                <div key={index} className="bg-white rounded-xl shadow-lg border border-gray-100 p-4">
                  <div className="flex justify-between items-start">
                    <div className="flex-1 pr-4">
                      <h4 className="font-semibold text-gray-900 text-sm leading-tight">
                        {item.service}
                      </h4>
                    </div>
                    <div className="text-right">
                      <span className="font-bold text-[#1e3a5f] text-sm">
                        {item.rate}
                      </span>
                    </div>
                  </div>
                </div>
              ))}
            </div>
          </motion.div>

          {/* Desktop Table Layout */}
          <motion.div variants={itemVariants} className="hidden md:block bg-white rounded-2xl shadow-xl overflow-hidden">
            <div className="overflow-x-auto">
              <table className="w-full">
                <thead>
                  <tr className="bg-[#1e3a5f] text-white">
                    <th className="py-4 px-6 text-left">Service</th>
                    <th className="py-4 px-6 text-right">Labor Rate / Fee</th>
                  </tr>
                </thead>
                <tbody>
                  {pricing.map((item, index) => <tr key={index} className={index % 2 === 0 ? 'bg-white' : 'bg-slate-50'}>
                      <td className="py-4 px-6 border-t border-gray-200">
                        {item.service}
                      </td>
                      <td className="py-4 px-6 text-right border-t border-gray-200 font-medium">
                        {item.rate}
                      </td>
                    </tr>)}
                </tbody>
              </table>
            </div>
          </motion.div>
          <motion.div variants={itemVariants} className="mt-6 text-gray-500 text-center">
            <p>
              * Transmission Rebuilding price depends on year, make, and model.
              Includes removal, installation, master kit, fluid, and cleaner
              with 1-year labor warranty.
            </p>
          </motion.div>
        </motion.div>
      </div>
    </section>;
};
const ServiceCertifications = () => {
  const ref = useRef(null);
  const isInView = useInView(ref, {
    once: true,
    margin: '-100px'
  });
  const controls = useAnimation();
  useEffect(() => {
    if (isInView) {
      controls.start('visible');
    }
  }, [isInView, controls]);
  const containerVariants = {
    hidden: {
      opacity: 0
    },
    visible: {
      opacity: 1,
      transition: {
        staggerChildren: 0.15,
        delayChildren: 0.2
      }
    }
  };
  const itemVariants = {
    hidden: {
      y: 30,
      opacity: 0
    },
    visible: {
      y: 0,
      opacity: 1,
      transition: {
        duration: 0.5,
        ease: 'easeOut'
      }
    }
  };
  const aseCertifications = ['ASE Master Automotive Technician', 'ASE Medium/Heavy Truck', 'ASE Heating & Air Conditioning', 'ASE Electrical/Electronic System', 'ASE Engine Repair', 'ASE Exhaust System', 'ASE Automatic Transmission/Transaxle', 'ASE Suspension & Steering', 'ASE Brakes'];
  const manufacturerCertifications = ['General Motors: Powertrain & Engine Management', 'Chrysler: Powertrain & Engine Management', 'Mercedes Benz: Powertrain & Engine Management', 'Ford Master Technician', 'Toyota Master Technician', 'Lexus Master Technician'];
  const supportedVehicles = ['Ford', 'Lexus', 'Toyota', 'Dodge', 'General Motors', 'Chrysler', 'Mercedes Benz', 'All makes and models for transmission work'];
  return <section ref={ref} className="py-12 sm:py-16 md:py-20 bg-white">
      <div className="container mx-auto px-4 md:px-8">
        <motion.div className="text-center mb-16" variants={containerVariants} initial="hidden" animate={controls}>
          <motion.span variants={itemVariants} className="inline-block bg-blue-100 text-blue-800 px-4 py-1 rounded-full text-sm font-semibold mb-3">
            OUR EXPERTISE
          </motion.span>
          <motion.h2 variants={itemVariants} className="text-4xl font-bold text-gray-900 mb-4">
            Certifications & Experience
          </motion.h2>
          <motion.p variants={itemVariants} className="text-xl text-gray-600 max-w-3xl mx-auto">
            Our team brings over 38 years of combined experience with extensive
            factory training and certifications.
          </motion.p>
        </motion.div>
        <div className="grid grid-cols-1 lg:grid-cols-3 gap-8">
          <motion.div className="bg-[#1e3a5f] rounded-2xl p-8 text-white shadow-xl" variants={itemVariants} initial="hidden" animate={controls}>
            <h3 className="text-2xl font-bold mb-6">ASE Certifications</h3>
            <ul className="space-y-3">
              {aseCertifications.map((cert, index) => <li key={index} className="flex items-start">
                  <CheckIcon className="h-5 w-5 text-blue-300 mr-3 mt-1 flex-shrink-0" />
                  <span>{cert}</span>
                </li>)}
            </ul>
          </motion.div>
          <motion.div className="bg-gradient-to-br from-blue-600 to-blue-800 rounded-2xl p-8 text-white shadow-xl" variants={itemVariants} initial="hidden" animate={controls}>
            <h3 className="text-2xl font-bold mb-6">
              Manufacturer Certifications
            </h3>
            <ul className="space-y-3">
              {manufacturerCertifications.map((cert, index) => <li key={index} className="flex items-start">
                  <CheckIcon className="h-5 w-5 text-blue-300 mr-3 mt-1 flex-shrink-0" />
                  <span>{cert}</span>
                </li>)}
            </ul>
          </motion.div>
          <motion.div className="bg-slate-800 rounded-2xl p-8 text-white shadow-xl" variants={itemVariants} initial="hidden" animate={controls}>
            <h3 className="text-2xl font-bold mb-6">
              Supported Vehicle Brands
            </h3>
            <ul className="space-y-3">
              {supportedVehicles.map((vehicle, index) => <li key={index} className="flex items-start">
                  <CheckIcon className="h-5 w-5 text-blue-300 mr-3 mt-1 flex-shrink-0" />
                  <span>{vehicle}</span>
                </li>)}
            </ul>
          </motion.div>
        </div>
      </div>
    </section>;
};
const ServiceBooking = () => {
  const ref = useRef(null);
  const isInView = useInView(ref, {
    once: true,
    margin: '-100px'
  });
  const controls = useAnimation();
  useEffect(() => {
    if (isInView) {
      controls.start('visible');
    }
  }, [isInView, controls]);
  const containerVariants = {
    hidden: {
      opacity: 0
    },
    visible: {
      opacity: 1,
      transition: {
        staggerChildren: 0.15,
        delayChildren: 0.2
      }
    }
  };
  const itemVariants = {
    hidden: {
      y: 30,
      opacity: 0
    },
    visible: {
      y: 0,
      opacity: 1,
      transition: {
        duration: 0.5,
        ease: 'easeOut'
      }
    }
  };
  return <section id="booking" ref={ref} className="py-12 sm:py-16 md:py-20 bg-slate-100 scroll-mt-16">
      <div className="container mx-auto px-4 md:px-8">
        <motion.div className="max-w-5xl mx-auto" variants={containerVariants} initial="hidden" animate={controls}>
          <motion.div variants={itemVariants} className="text-center mb-12">
            <span className="inline-block bg-blue-100 text-blue-800 px-4 py-1 rounded-full text-sm font-semibold mb-3">
              GET STARTED
            </span>
            <h2 className="text-4xl font-bold text-gray-900 mb-4">
              Book Your Service Appointment
            </h2>
            <p className="text-xl text-gray-600 max-w-3xl mx-auto">
              Fill out the form below to request a quote for your vehicle
              service. Our team will contact you within 24 hours.
            </p>
          </motion.div>
          <motion.div variants={itemVariants}>
            <ServiceForm />
          </motion.div>
        </motion.div>
      </div>
    </section>;
};
export default Services;