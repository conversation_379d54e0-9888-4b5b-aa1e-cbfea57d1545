# Image Assets Directory

This directory contains all image assets for the ADJ Automotive Repair Services application.

## Folder Structure

```
public/images/
├── logos/          # Company logos, brand assets
├── banners/        # Hero images, promotional banners
├── cars/           # Car images for inventory/sales
├── services/       # Service-related images
└── README.md       # This file
```

## Usage Guidelines

### Logos (`/logos/`)
- Company logos (PNG with transparent background recommended)
- Brand icons and symbols
- Favicon and app icons

### Banners (`/banners/`)
- Hero section images
- Promotional banners
- Background images for key sections

### Cars (`/cars/`)
- Vehicle inventory images
- Car detail photos
- Before/after repair images

### Services (`/services/`)
- Service category icons
- Repair process images
- Equipment and tools photos

## File Naming Convention

Use descriptive, lowercase names with hyphens:
- `company-logo.png`
- `hero-banner.jpg`
- `toyota-camry-2023.jpg`
- `oil-change-service.jpg`

## Supported Formats

- **PNG**: For logos, icons, and images requiring transparency
- **JPG/JPEG**: For photographs and complex images
- **SVG**: For scalable icons and graphics
- **WebP**: For optimized web images (with fallbacks)

## Image Optimization

Before uploading:
1. Resize images to appropriate dimensions
2. Compress images for web use
3. Use appropriate formats for the content type
4. Consider using WebP format with fallbacks for better performance

## Accessing Images in Code

In React components, reference images like this:
```jsx
// For images in public/images/
<img src="/images/logos/company-logo.png" alt="Company Logo" />

// For images in src/assets/ (if using Vite)
import logo from '../assets/images/logo.png'
<img src={logo} alt="Logo" />
``` 