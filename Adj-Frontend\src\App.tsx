import { useEffect } from 'react';
import { BrowserRouter as Router, Routes, Route, useLocation } from 'react-router-dom';
import { AnimatePresence } from 'framer-motion';
import Navbar from './components/Navbar';
import Footer from './components/Footer';
import Home from './pages/Home';
import Services from './pages/Services';
import CarsForSale from './pages/CarsForSale';
import CarDetail from './pages/CarDetail';
import About from './pages/About';
import Contact from './pages/Contact';
import BookService from './pages/BookService';
import ProtectedRoute from './components/ProtectedRoute';
import AdminLogin from './admin/Login';
import AdminDashboard from './admin/Dashboard';
import AdminCarManagement from './admin/CarManagement';
import AdminCarInquiries from './admin/CarInquiries';
import AdminServiceRequests from './admin/ServiceRequests';
import AdminCustomerDatabase from './admin/CustomerDatabase';
import AdminReports from './admin/Reports';
import AdminSettings from './admin/Settings';
const AppContent = () => {
  const location = useLocation();
  const isAdminRoute = location.pathname.startsWith('/admin');

  // Intelligent scroll restoration that handles anchor links
  useEffect(() => {
    if (location.hash) {
      // If there's a hash (anchor link), scroll to that element
      const element = document.getElementById(location.hash.substring(1));
      if (element) {
        // Use setTimeout to ensure the element is rendered
        setTimeout(() => {
          element.scrollIntoView({ behavior: 'smooth' });
        }, 100);
      }
    } else {
      // No hash, scroll to top
      window.scrollTo(0, 0);
    }
  }, [location.pathname, location.hash]);

  if (isAdminRoute) {
    // Admin layout without Navbar and Footer
    return (
      <div className="h-screen bg-slate-100">
        <AnimatePresence mode="wait">
          <Routes>
            <Route path="/admin/login" element={<AdminLogin />} />
            <Route path="/admin" element={<ProtectedRoute><AdminDashboard /></ProtectedRoute>} />
            <Route path="/admin/cars" element={<ProtectedRoute><AdminCarManagement /></ProtectedRoute>} />
            <Route path="/admin/car-inquiries" element={<ProtectedRoute><AdminCarInquiries /></ProtectedRoute>} />
            <Route path="/admin/service-requests" element={<ProtectedRoute><AdminServiceRequests /></ProtectedRoute>} />
            <Route path="/admin/customers" element={<ProtectedRoute><AdminCustomerDatabase /></ProtectedRoute>} />
            <Route path="/admin/reports" element={<ProtectedRoute><AdminReports /></ProtectedRoute>} />
            <Route path="/admin/settings" element={<ProtectedRoute><AdminSettings /></ProtectedRoute>} />
          </Routes>
        </AnimatePresence>
      </div>
    );
  }

  // User layout with Navbar and Footer
  return (
    <div className="flex flex-col min-h-screen bg-slate-100">
      <Navbar />
      <AnimatePresence mode="wait">
        <Routes>
          <Route path="/" element={<Home />} />
          <Route path="/services" element={<Services />} />
          <Route path="/cars" element={<CarsForSale />} />
          <Route path="/cars/:id" element={<CarDetail />} />
          <Route path="/about" element={<About />} />
          <Route path="/contact" element={<Contact />} />
          <Route path="/book-service" element={<BookService />} />
        </Routes>
      </AnimatePresence>
      <Footer />
    </div>
  );
};

export function App() {
  return (
    <Router>
      <AppContent />
    </Router>
  );
}