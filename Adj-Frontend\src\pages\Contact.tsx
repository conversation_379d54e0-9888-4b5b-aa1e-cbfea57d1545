import React, { useEffect, useState, useRef, Children } from 'react';
import { motion, useAnimation, useInView } from 'framer-motion';
import { PhoneIcon, MailIcon, MapPinIcon, ClockIcon, CheckCircleIcon } from 'lucide-react';
const Contact = () => {
  return <main className="w-full">
      <ContactHero />
      <ContactInfo />
      <ContactForm />
      <LocationMap />
      <Faq />
    </main>;
};
const ContactHero = () => {
  const containerVariants = {
    hidden: {
      opacity: 0
    },
    visible: {
      opacity: 1,
      transition: {
        staggerChildren: 0.2
      }
    }
  };
  const itemVariants = {
    hidden: {
      y: 30,
      opacity: 0
    },
    visible: {
      y: 0,
      opacity: 1,
      transition: {
        duration: 0.6,
        ease: 'easeOut'
      }
    }
  };
  return <section className="relative py-16 bg-[#1e3a5f]">
      <div className="absolute inset-0 z-0">
        <div className="absolute inset-0 bg-black/50 z-10" style={{
        backgroundImage: 'linear-gradient(to bottom, rgba(30,58,95,0.8), rgba(15,37,66,0.95))'
      }}></div>
        <div className="w-full h-full bg-cover bg-center" style={{
        backgroundImage: "url('https://images.unsplash.com/photo-1580273916550-e323be2ae537?ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D&auto=format&fit=crop&w=1460&q=80')"
      }}></div>
      </div>
      <div className="container mx-auto px-4 md:px-8 relative z-10">
        <motion.div className="max-w-3xl mx-auto text-center" variants={containerVariants} initial="hidden" animate="visible">
          <motion.span variants={itemVariants} className="inline-block bg-blue-700 text-white px-4 py-1 rounded-full text-sm font-semibold mb-3">
            CONTACT US
          </motion.span>
          <motion.h1 variants={itemVariants} className="text-4xl md:text-5xl font-bold text-white mb-6 leading-tight">
            Get in Touch with Our Team
          </motion.h1>
          <motion.p variants={itemVariants} className="text-lg md:text-xl text-blue-100 mb-8">
            Have questions about our services or want to schedule an
            appointment? We're here to help.
          </motion.p>
        </motion.div>
      </div>
    </section>;
};
const ContactInfo = () => {
  const ref = useRef(null);
  const isInView = useInView(ref, {
    once: true,
    margin: '-100px'
  });
  const controls = useAnimation();
  useEffect(() => {
    if (isInView) {
      controls.start('visible');
    }
  }, [isInView, controls]);
  const containerVariants = {
    hidden: {
      opacity: 0
    },
    visible: {
      opacity: 1,
      transition: {
        staggerChildren: 0.15,
        delayChildren: 0.2
      }
    }
  };
  const itemVariants = {
    hidden: {
      y: 30,
      opacity: 0
    },
    visible: {
      y: 0,
      opacity: 1,
      transition: {
        duration: 0.5,
        ease: 'easeOut'
      }
    }
  };
  return <section ref={ref} className="py-16 bg-white">
      <div className="container mx-auto px-4 md:px-8">
        <motion.div variants={containerVariants} initial="hidden" animate={controls} className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-8">
          <motion.div variants={itemVariants} className="bg-white rounded-2xl shadow-xl p-6">
            <div className="bg-blue-100 p-4 rounded-xl inline-block mb-4">
              <PhoneIcon className="h-6 w-6 text-blue-600" />
            </div>
            <h3 className="text-xl font-bold text-gray-900 mb-2">Phone</h3>
            <p className="text-gray-600 mb-4">
              Call us directly for immediate assistance
            </p>
            <a href="tel:+16714838335" className="text-blue-600 font-semibold hover:text-blue-800 transition-colors">
              (*************
            </a>
          </motion.div>
          <motion.div variants={itemVariants} className="bg-white rounded-2xl shadow-xl p-6">
            <div className="bg-blue-100 p-4 rounded-xl inline-block mb-4">
              <MailIcon className="h-6 w-6 text-blue-600" />
            </div>
            <h3 className="text-xl font-bold text-gray-900 mb-2">Email</h3>
            <p className="text-gray-600 mb-4">
              Send us an email for general inquiries
            </p>
            <a href="mailto:<EMAIL>" className="text-blue-600 font-semibold hover:text-blue-800 transition-colors">
              <EMAIL>
            </a>
          </motion.div>
          <motion.div variants={itemVariants} className="bg-white rounded-2xl shadow-xl p-6">
            <div className="bg-blue-100 p-4 rounded-xl inline-block mb-4">
              <MapPinIcon className="h-6 w-6 text-blue-600" />
            </div>
            <h3 className="text-xl font-bold text-gray-900 mb-2">Location</h3>
            <p className="text-gray-600 mb-4">Visit our service center</p>
            <address className="text-blue-600 font-semibold not-italic">
              125 Chalan Ayuyu Yigo,
              <br />
              Guam 96929
            </address>
          </motion.div>
          <motion.div variants={itemVariants} className="bg-white rounded-2xl shadow-xl p-6">
            <div className="bg-blue-100 p-4 rounded-xl inline-block mb-4">
              <ClockIcon className="h-6 w-6 text-blue-600" />
            </div>
            <h3 className="text-xl font-bold text-gray-900 mb-2">Hours</h3>
            <p className="text-gray-600 mb-4">Our regular business hours</p>
            <div className="space-y-1">
              <p className="flex justify-between">
                <span className="text-gray-600">Mon-Fri:</span>
                <span className="font-semibold">8:00 AM - 5:00 PM</span>
              </p>
              <p className="flex justify-between">
                <span className="text-gray-600">Saturday:</span>
                <span className="font-semibold">9:00 AM - 2:00 PM</span>
              </p>
              <p className="flex justify-between">
                <span className="text-gray-600">Sunday:</span>
                <span className="font-semibold">Closed</span>
              </p>
            </div>
          </motion.div>
        </motion.div>
      </div>
    </section>;
};
const ContactForm = () => {
  const [formData, setFormData] = useState({
    fullName: '',
    email: '',
    phone: '',
    subject: '',
    message: ''
  });
  const [formStatus, setFormStatus] = useState({
    submitted: false,
    submitting: false,
    error: null
  });
  const handleChange = e => {
    const {
      name,
      value
    } = e.target;
    setFormData(prev => ({
      ...prev,
      [name]: value
    }));
  };
  const handleSubmit = e => {
    e.preventDefault();
    setFormStatus({
      submitted: false,
      submitting: true,
      error: null
    });
    // Simulate form submission
    setTimeout(() => {
      setFormStatus({
        submitted: true,
        submitting: false,
        error: null
      });
      // In a real app, you would send this data to your backend
      console.log('Form submitted:', formData);
    }, 1500);
  };
  const ref = useRef(null);
  const isInView = useInView(ref, {
    once: true,
    margin: '-100px'
  });
  const controls = useAnimation();
  useEffect(() => {
    if (isInView) {
      controls.start('visible');
    }
  }, [isInView, controls]);
  const containerVariants = {
    hidden: {
      opacity: 0
    },
    visible: {
      opacity: 1,
      transition: {
        staggerChildren: 0.15,
        delayChildren: 0.2
      }
    }
  };
  const itemVariants = {
    hidden: {
      y: 30,
      opacity: 0
    },
    visible: {
      y: 0,
      opacity: 1,
      transition: {
        duration: 0.5,
        ease: 'easeOut'
      }
    }
  };
  const successVariants = {
    hidden: {
      scale: 0.8,
      opacity: 0
    },
    visible: {
      scale: 1,
      opacity: 1,
      transition: {
        type: 'spring',
        stiffness: 400,
        damping: 10
      }
    }
  };
  return <section ref={ref} className="py-16 bg-slate-100">
      <div className="container mx-auto px-4 md:px-8">
        <motion.div variants={containerVariants} initial="hidden" animate={controls} className="max-w-4xl mx-auto">
          <motion.div variants={itemVariants} className="text-center mb-12">
            <span className="inline-block bg-blue-100 text-blue-800 px-4 py-1 rounded-full text-sm font-semibold mb-3">
              GET IN TOUCH
            </span>
            <h2 className="text-3xl font-bold text-gray-900 mb-4">
              Send Us a Message
            </h2>
            <p className="text-lg text-gray-600 max-w-2xl mx-auto">
              Have questions, feedback, or want to schedule a service? Fill out
              the form below and we'll get back to you as soon as possible.
            </p>
          </motion.div>
          {formStatus.submitted ? <motion.div className="bg-white rounded-2xl p-8 shadow-xl text-center" variants={successVariants} initial="hidden" animate="visible">
              <div className="mx-auto w-16 h-16 bg-green-100 rounded-full flex items-center justify-center mb-6">
                <CheckCircleIcon className="h-10 w-10 text-green-600" />
              </div>
              <h3 className="text-2xl font-bold text-gray-900 mb-2">
                Message Sent Successfully!
              </h3>
              <p className="text-gray-600 mb-6">
                Thank you for contacting ADJ Automotive. We've received your
                message and will get back to you within 24 hours.
              </p>
              <button onClick={() => setFormStatus({
            submitted: false,
            submitting: false,
            error: null
          })} className="bg-[#1e3a5f] hover:bg-blue-800 text-white font-semibold py-3 px-8 rounded-lg transition-colors">
                Send Another Message
              </button>
            </motion.div> : <motion.form onSubmit={handleSubmit} className="bg-white rounded-2xl p-8 shadow-xl" variants={itemVariants}>
              <div className="grid grid-cols-1 md:grid-cols-2 gap-6 mb-6">
                <div>
                  <label htmlFor="fullName" className="block text-gray-700 font-medium mb-2">
                    Full Name *
                  </label>
                  <input type="text" id="fullName" name="fullName" value={formData.fullName} onChange={handleChange} required className="w-full px-4 py-3 rounded-lg border border-gray-300 focus:ring-2 focus:ring-blue-500 focus:border-transparent transition-all" />
                </div>
                <div>
                  <label htmlFor="email" className="block text-gray-700 font-medium mb-2">
                    Email Address *
                  </label>
                  <input type="email" id="email" name="email" value={formData.email} onChange={handleChange} required className="w-full px-4 py-3 rounded-lg border border-gray-300 focus:ring-2 focus:ring-blue-500 focus:border-transparent transition-all" />
                </div>
              </div>
              <div className="grid grid-cols-1 md:grid-cols-2 gap-6 mb-6">
                <div>
                  <label htmlFor="phone" className="block text-gray-700 font-medium mb-2">
                    Phone Number
                  </label>
                  <input type="tel" id="phone" name="phone" value={formData.phone} onChange={handleChange} className="w-full px-4 py-3 rounded-lg border border-gray-300 focus:ring-2 focus:ring-blue-500 focus:border-transparent transition-all" />
                </div>
                <div>
                  <label htmlFor="subject" className="block text-gray-700 font-medium mb-2">
                    Subject *
                  </label>
                  <select id="subject" name="subject" value={formData.subject} onChange={handleChange} required className="w-full px-4 py-3 rounded-lg border border-gray-300 focus:ring-2 focus:ring-blue-500 focus:border-transparent transition-all">
                    <option value="">Select a subject</option>
                    <option value="Service Inquiry">Service Inquiry</option>
                    <option value="Vehicle Purchase">Vehicle Purchase</option>
                    <option value="Parts Inquiry">Parts Inquiry</option>
                    <option value="Employment">Employment</option>
                    <option value="General Question">General Question</option>
                    <option value="Feedback">Feedback</option>
                  </select>
                </div>
              </div>
              <div className="mb-6">
                <label htmlFor="message" className="block text-gray-700 font-medium mb-2">
                  Message *
                </label>
                <textarea id="message" name="message" value={formData.message} onChange={handleChange} required rows={6} className="w-full px-4 py-3 rounded-lg border border-gray-300 focus:ring-2 focus:ring-blue-500 focus:border-transparent transition-all" placeholder="How can we help you today?"></textarea>
              </div>
              <div className="flex items-start mb-6">
                <input type="checkbox" id="consent" className="mt-1 mr-2" required />
                <label htmlFor="consent" className="text-sm text-gray-600">
                  I consent to having ADJ Automotive store my submitted
                  information so they can respond to my inquiry.
                </label>
              </div>
              <button type="submit" disabled={formStatus.submitting} className={`w-full bg-[#1e3a5f] hover:bg-blue-800 text-white font-semibold py-3 px-6 rounded-lg transition-colors ${formStatus.submitting ? 'opacity-70 cursor-not-allowed' : ''}`}>
                {formStatus.submitting ? 'Sending...' : 'Send Message'}
              </button>
            </motion.form>}
        </motion.div>
      </div>
    </section>;
};
const LocationMap = () => {
  const ref = useRef(null);
  const isInView = useInView(ref, {
    once: true,
    margin: '-100px'
  });
  const controls = useAnimation();
  useEffect(() => {
    if (isInView) {
      controls.start('visible');
    }
  }, [isInView, controls]);
  const containerVariants = {
    hidden: {
      opacity: 0
    },
    visible: {
      opacity: 1,
      transition: {
        staggerChildren: 0.15,
        delayChildren: 0.2
      }
    }
  };
  const itemVariants = {
    hidden: {
      y: 30,
      opacity: 0
    },
    visible: {
      y: 0,
      opacity: 1,
      transition: {
        duration: 0.5,
        ease: 'easeOut'
      }
    }
  };
  return <section ref={ref} className="py-16 bg-white">
      <div className="container mx-auto px-4 md:px-8">
        <motion.div variants={containerVariants} initial="hidden" animate={controls} className="max-w-6xl mx-auto">
          <motion.div variants={itemVariants} className="text-center mb-12">
            <span className="inline-block bg-blue-100 text-blue-800 px-4 py-1 rounded-full text-sm font-semibold mb-3">
              FIND US
            </span>
            <h2 className="text-3xl font-bold text-gray-900 mb-4">
              Our Location
            </h2>
            <p className="text-lg text-gray-600 max-w-2xl mx-auto">
              Conveniently located in Yigo, our service center is easy to find
              and accessible from all areas of Guam.
            </p>
          </motion.div>
          <motion.div variants={itemVariants} className="bg-white rounded-2xl overflow-hidden shadow-xl">
            <div className="w-full h-[400px]">
              <iframe
                title="ADJ Automotive location"
                src="https://www.google.com/maps/embed?pb=!1m17!1m11!1m3!1d988560.368673327!2d120.15979830510544!3d14.56691118655384!2m2!1f0!2f0!3m2!1i1024!2i768!4f13.1!3m3!1m2!1s0x671f875d0b06cf21%3A0x528a4e311834a6bd!2sADJ%20Automotive!5e0!3m2!1sen!2sph!4v1754723033884!5m2!1sen!2sph"
                className="w-full h-full border-0"
                loading="lazy"
                allowFullScreen
                referrerPolicy="no-referrer-when-downgrade"
              />
            </div>
            <div className="p-4 text-center">
              <a
                href="https://maps.google.com/?q=125+Chalan+Ayuyu+Yigo+Guam+96929"
                target="_blank"
                rel="noopener noreferrer"
                className="inline-block bg-[#1e3a5f] hover:bg-blue-800 text-white font-semibold py-2 px-6 rounded-lg transition-colors"
              >
                Get Directions
              </a>
            </div>
          </motion.div>
        </motion.div>
      </div>
    </section>;
};
const Faq = () => {
  const ref = useRef(null);
  const isInView = useInView(ref, {
    once: true,
    margin: '-100px'
  });
  const controls = useAnimation();
  useEffect(() => {
    if (isInView) {
      controls.start('visible');
    }
  }, [isInView, controls]);
  const containerVariants = {
    hidden: {
      opacity: 0
    },
    visible: {
      opacity: 1,
      transition: {
        staggerChildren: 0.15,
        delayChildren: 0.2
      }
    }
  };
  const itemVariants = {
    hidden: {
      y: 30,
      opacity: 0
    },
    visible: {
      y: 0,
      opacity: 1,
      transition: {
        duration: 0.5,
        ease: 'easeOut'
      }
    }
  };
  const faqs = [{
    question: 'Do I need an appointment for service?',
    answer: 'While we do accept walk-ins for minor services, we recommend scheduling an appointment for major repairs like transmission work or engine diagnostics. This ensures we have the necessary parts and time available to properly service your vehicle.'
  }, {
    question: 'How long does a typical transmission rebuild take?',
    answer: "A complete transmission rebuild typically takes 3-5 business days, depending on the vehicle make and model. We'll provide you with a more specific timeline during your initial consultation."
  }, {
    question: 'Do you offer warranty on your repairs?',
    answer: 'Yes, we offer a 1-year/12,000-mile warranty on transmission rebuilds and a 90-day warranty on most other repairs. This warranty covers both parts and labor.'
  }, {
    question: 'What forms of payment do you accept?',
    answer: 'We accept all major credit cards, cash, personal checks (with valid ID), and offer financing options for larger repairs through our financing partners.'
  }, {
    question: 'Do you provide loaner vehicles?',
    answer: 'We offer a limited number of loaner vehicles for customers having major repairs done. These are available on a first-come, first-served basis and require advance reservation.'
  }, {
    question: 'Can I bring my own parts for installation?',
    answer: "While we understand the desire to source your own parts, we generally don't install customer-supplied parts. This is because we can't warranty parts we didn't source, and we want to ensure the highest quality repairs for your vehicle."
  }];
  return <section ref={ref} className="py-16 bg-slate-100">
      <div className="container mx-auto px-4 md:px-8">
        <motion.div variants={containerVariants} initial="hidden" animate={controls} className="max-w-4xl mx-auto">
          <motion.div variants={itemVariants} className="text-center mb-12">
            <span className="inline-block bg-blue-100 text-blue-800 px-4 py-1 rounded-full text-sm font-semibold mb-3">
              FAQ
            </span>
            <h2 className="text-3xl font-bold text-gray-900 mb-4">
              Frequently Asked Questions
            </h2>
            <p className="text-lg text-gray-600 max-w-2xl mx-auto">
              Find answers to our most commonly asked questions. If you don't
              see what you're looking for, feel free to contact us directly.
            </p>
          </motion.div>
          <motion.div variants={itemVariants} className="space-y-6">
            {faqs.map((faq, index) => <FaqItem key={index} question={faq.question} answer={faq.answer} />)}
          </motion.div>
          <motion.div variants={itemVariants} className="text-center mt-12">
            <p className="text-gray-600 mb-6">
              Still have questions? We're here to help.
            </p>
            <a href="tel:+16714838335" className="bg-[#1e3a5f] hover:bg-blue-800 text-white font-semibold py-3 px-8 rounded-lg transition-colors inline-flex items-center">
              <PhoneIcon className="h-5 w-5 mr-2" />
              Call Us: (*************
            </a>
          </motion.div>
        </motion.div>
      </div>
    </section>;
};
const FaqItem = ({
  question,
  answer
}) => {
  const [isOpen, setIsOpen] = useState(false);
  return <div className="bg-white rounded-xl shadow-md overflow-hidden">
      <button className="w-full px-6 py-4 text-left font-semibold text-gray-900 flex justify-between items-center" onClick={() => setIsOpen(!isOpen)}>
        {question}
        <svg xmlns="http://www.w3.org/2000/svg" className={`h-5 w-5 transition-transform ${isOpen ? 'transform rotate-180' : ''}`} fill="none" viewBox="0 0 24 24" stroke="currentColor">
          <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M19 9l-7 7-7-7" />
        </svg>
      </button>
      <motion.div initial={{
      height: 0,
      opacity: 0
    }} animate={{
      height: isOpen ? 'auto' : 0,
      opacity: isOpen ? 1 : 0
    }} transition={{
      duration: 0.3
    }} className="overflow-hidden">
        <div className="px-6 pb-4 text-gray-600">{answer}</div>
      </motion.div>
    </div>;
};
export default Contact;