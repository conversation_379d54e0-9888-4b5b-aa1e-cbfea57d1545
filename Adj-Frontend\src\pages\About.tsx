import React, { useEffect, useRef } from 'react';
import { motion, useAnimation, useInView } from 'framer-motion';
import { Link } from 'react-router-dom';
import { CheckCircleIcon, AwardIcon, UsersIcon, BarChart3Icon, ArrowRightIcon } from 'lucide-react';
const About = () => {
  return <main className="w-full">
      <HeroSection />
      <OurStory />
      <OurValues />
      <OurTeam />
      <Certifications />
      <Testimonials />
      <CtaSection />
    </main>;
};
const HeroSection = () => {
  const containerVariants = {
    hidden: {
      opacity: 0
    },
    visible: {
      opacity: 1,
      transition: {
        staggerChildren: 0.2
      }
    }
  };
  const itemVariants = {
    hidden: {
      y: 30,
      opacity: 0
    },
    visible: {
      y: 0,
      opacity: 1,
      transition: {
        duration: 0.6,
        ease: 'easeOut'
      }
    }
  };
  return <section className="relative py-20 bg-[#1e3a5f]">
      <div className="absolute inset-0 z-0">
        <div className="absolute inset-0 bg-black/60 z-10" style={{
        backgroundImage: 'linear-gradient(to bottom, rgba(30,58,95,0.8), rgba(15,37,66,0.95))'
      }}></div>
        <div className="w-full h-full bg-cover bg-center" style={{
        backgroundImage: "url('https://images.unsplash.com/photo-1613214149922-f1809c99b414?ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D&auto=format&fit=crop&w=1470&q=80')"
      }}></div>
      </div>
      <div className="container mx-auto px-4 md:px-8 relative z-10">
        <motion.div className="max-w-3xl" variants={containerVariants} initial="hidden" animate="visible">
          <motion.span variants={itemVariants} className="inline-block bg-blue-700 text-white px-4 py-1 rounded-full text-sm font-semibold mb-3">
            ABOUT US
          </motion.span>
          <motion.h1 variants={itemVariants} className="text-4xl md:text-5xl lg:text-6xl font-bold text-white mb-6 leading-tight">
            Your Trusted Automotive Experts Since 2005
          </motion.h1>
          <motion.p variants={itemVariants} className="text-lg md:text-xl text-blue-100 mb-8">
            ADJ Automotive is a veteran-owned business committed to providing
            dealership-quality repairs at affordable prices, with a focus on
            honesty, integrity, and exceptional customer service.
          </motion.p>
        </motion.div>
      </div>
    </section>;
};
const OurStory = () => {
  const ref = useRef(null);
  const isInView = useInView(ref, {
    once: true,
    margin: '-100px'
  });
  const controls = useAnimation();
  useEffect(() => {
    if (isInView) {
      controls.start('visible');
    }
  }, [isInView, controls]);
  const containerVariants = {
    hidden: {
      opacity: 0
    },
    visible: {
      opacity: 1,
      transition: {
        staggerChildren: 0.15,
        delayChildren: 0.2
      }
    }
  };
  const itemVariants = {
    hidden: {
      y: 30,
      opacity: 0
    },
    visible: {
      y: 0,
      opacity: 1,
      transition: {
        duration: 0.5,
        ease: 'easeOut'
      }
    }
  };
  return <section ref={ref} className="py-20 bg-white">
      <div className="container mx-auto px-4 md:px-8">
        <motion.div variants={containerVariants} initial="hidden" animate={controls} className="flex flex-col lg:flex-row gap-12 items-center">
          <motion.div variants={itemVariants} className="lg:w-1/2">
            <span className="inline-block bg-blue-100 text-blue-800 px-4 py-1 rounded-full text-sm font-semibold mb-3">
              OUR STORY
            </span>
            <h2 className="text-4xl font-bold text-gray-900 mb-6">
              A Legacy of Automotive Excellence
            </h2>
            <div className="space-y-4 text-gray-700">
              <p>
                Founded in 2005 by Adam Johnson, a U.S. Navy veteran with a
                passion for vehicles and mechanical excellence, ADJ Automotive
                began as a small transmission repair shop serving the local
                community.
              </p>
              <p>
                With a commitment to honest service, fair pricing, and
                exceptional workmanship, our reputation quickly grew. Within
                five years, we expanded our services to include comprehensive
                engine repair, advanced diagnostics, and general automotive
                maintenance.
              </p>
              <p>
                Today, ADJ Automotive has grown into a full-service repair
                facility with state-of-the-art equipment and a team of
                ASE-certified technicians. Despite our growth, we maintain the
                same core values that drove our success from day one: integrity,
                quality, and customer satisfaction.
              </p>
              <p>
                As a federally registered veteran-owned small business, we take
                pride in serving our community with the same dedication and
                attention to detail that defines military service. Our team
                brings over 38 years of combined experience, with extensive
                factory training and certifications across multiple vehicle
                brands.
              </p>
            </div>
          </motion.div>
          <motion.div variants={itemVariants} className="lg:w-1/2">
            <div className="grid grid-cols-2 gap-4">
              <div className="rounded-2xl overflow-hidden shadow-xl h-64">
                <img src="https://images.unsplash.com/photo-1613214149922-f1809c99b414?ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D&auto=format&fit=crop&w=1470&q=80" alt="ADJ Automotive Shop Exterior" className="w-full h-full object-cover" />
              </div>
              <div className="rounded-2xl overflow-hidden shadow-xl h-64">
                <img src="https://images.unsplash.com/photo-1517524008697-84bbe3c3fd98?ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D&auto=format&fit=crop&w=1528&q=80" alt="Transmission Repair" className="w-full h-full object-cover" />
              </div>
              <div className="rounded-2xl overflow-hidden shadow-xl h-64">
                <img src="https://images.unsplash.com/photo-1619642751034-765dfdf7c58e?ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D&auto=format&fit=crop&w=1335&q=80" alt="Engine Repair" className="w-full h-full object-cover" />
              </div>
              <div className="rounded-2xl overflow-hidden shadow-xl h-64">
                <img src="https://images.unsplash.com/photo-1603553329474-99f95f35394f?ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D&auto=format&fit=crop&w=1470&q=80" alt="Vehicle Diagnostics" className="w-full h-full object-cover" />
              </div>
            </div>
          </motion.div>
        </motion.div>
      </div>
    </section>;
};
const OurValues = () => {
  const ref = useRef(null);
  const isInView = useInView(ref, {
    once: true,
    margin: '-100px'
  });
  const controls = useAnimation();
  useEffect(() => {
    if (isInView) {
      controls.start('visible');
    }
  }, [isInView, controls]);
  const containerVariants = {
    hidden: {
      opacity: 0
    },
    visible: {
      opacity: 1,
      transition: {
        staggerChildren: 0.15,
        delayChildren: 0.2
      }
    }
  };
  const itemVariants = {
    hidden: {
      y: 30,
      opacity: 0
    },
    visible: {
      y: 0,
      opacity: 1,
      transition: {
        duration: 0.5,
        ease: 'easeOut'
      }
    }
  };
  const values = [{
    title: 'Integrity',
    description: "We believe in complete transparency and honesty in all our dealings. We'll never recommend unnecessary repairs or services.",
    icon: <CheckCircleIcon className="h-10 w-10 text-blue-600" />
  }, {
    title: 'Excellence',
    description: 'We strive for excellence in every repair, using quality parts and following manufacturer specifications for lasting results.',
    icon: <AwardIcon className="h-10 w-10 text-blue-600" />
  }, {
    title: 'Customer Focus',
    description: "Your satisfaction is our priority. We take the time to explain repairs, answer questions, and ensure you're completely satisfied.",
    icon: <UsersIcon className="h-10 w-10 text-blue-600" />
  }, {
    title: 'Continuous Improvement',
    description: 'We invest in ongoing training and the latest diagnostic equipment to stay ahead of automotive technology advances.',
    icon: <BarChart3Icon className="h-10 w-10 text-blue-600" />
  }];
  return <section ref={ref} className="py-20 bg-slate-100">
      <div className="container mx-auto px-4 md:px-8">
        <motion.div variants={containerVariants} initial="hidden" animate={controls} className="text-center max-w-3xl mx-auto mb-16">
          <motion.span variants={itemVariants} className="inline-block bg-blue-100 text-blue-800 px-4 py-1 rounded-full text-sm font-semibold mb-3">
            OUR VALUES
          </motion.span>
          <motion.h2 variants={itemVariants} className="text-4xl font-bold text-gray-900 mb-4">
            The Principles That Guide Us
          </motion.h2>
          <motion.p variants={itemVariants} className="text-lg text-gray-600">
            At ADJ Automotive, our core values shape every interaction and
            repair decision we make. These principles have guided our business
            since day one.
          </motion.p>
        </motion.div>
        <motion.div variants={containerVariants} initial="hidden" animate={controls} className="grid grid-cols-1 md:grid-cols-2 gap-8">
          {values.map((value, index) => <motion.div key={index} variants={itemVariants} className="bg-white rounded-2xl p-8 shadow-xl">
              <div className="bg-blue-100 p-4 rounded-xl inline-block mb-6">
                {value.icon}
              </div>
              <h3 className="text-2xl font-bold text-gray-900 mb-3">
                {value.title}
              </h3>
              <p className="text-gray-600">{value.description}</p>
            </motion.div>)}
        </motion.div>
      </div>
    </section>;
};
const OurTeam = () => {
  const ref = useRef(null);
  const isInView = useInView(ref, {
    once: true,
    margin: '-100px'
  });
  const controls = useAnimation();
  useEffect(() => {
    if (isInView) {
      controls.start('visible');
    }
  }, [isInView, controls]);
  const containerVariants = {
    hidden: {
      opacity: 0
    },
    visible: {
      opacity: 1,
      transition: {
        staggerChildren: 0.15,
        delayChildren: 0.2
      }
    }
  };
  const itemVariants = {
    hidden: {
      y: 30,
      opacity: 0
    },
    visible: {
      y: 0,
      opacity: 1,
      transition: {
        duration: 0.5,
        ease: 'easeOut'
      }
    }
  };
  const team = [{
    name: 'Adam Johnson',
    role: 'Founder & Master Technician',
    image: 'https://images.unsplash.com/photo-1560250097-0b93528c311a?ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D&auto=format&fit=crop&w=687&q=80',
    bio: 'U.S. Navy veteran with over 20 years of automotive experience. ASE Master Certified with specializations in transmission rebuilding and diagnostics.'
  }, {
    name: 'David Rodriguez',
    role: 'Service Manager',
    image: 'https://images.unsplash.com/photo-1472099645785-5658abf4ff4e?ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D&auto=format&fit=crop&w=1470&q=80',
    bio: '15 years of service management experience. Expert in customer relations and ensuring every repair meets our high quality standards.'
  }, {
    name: 'Sarah Williams',
    role: 'Master Technician',
    image: 'https://images.unsplash.com/photo-1573497019940-1c28c88b4f3e?ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D&auto=format&fit=crop&w=687&q=80',
    bio: 'ASE Master Certified with Ford and Toyota factory training. Specializes in electrical diagnostics and engine performance.'
  }, {
    name: 'Michael Chen',
    role: 'Diagnostic Specialist',
    image: 'https://images.unsplash.com/photo-1507003211169-0a1dd7228f2d?ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D&auto=format&fit=crop&w=687&q=80',
    bio: 'Expert in advanced vehicle diagnostics with certifications from multiple manufacturers. Solves the most challenging vehicle issues.'
  }];
  return <section ref={ref} className="py-20 bg-white">
      <div className="container mx-auto px-4 md:px-8">
        <motion.div variants={containerVariants} initial="hidden" animate={controls} className="text-center max-w-3xl mx-auto mb-16">
          <motion.span variants={itemVariants} className="inline-block bg-blue-100 text-blue-800 px-4 py-1 rounded-full text-sm font-semibold mb-3">
            OUR TEAM
          </motion.span>
          <motion.h2 variants={itemVariants} className="text-4xl font-bold text-gray-900 mb-4">
            Meet Our Expert Team
          </motion.h2>
          <motion.p variants={itemVariants} className="text-lg text-gray-600">
            Our team of ASE-certified technicians brings decades of combined
            experience and a passion for automotive excellence to every repair.
          </motion.p>
        </motion.div>
        <motion.div variants={containerVariants} initial="hidden" animate={controls} className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-8">
          {team.map((member, index) => <motion.div key={index} variants={itemVariants} className="bg-white rounded-2xl overflow-hidden shadow-xl">
              <div className="h-64 overflow-hidden">
                <img src={member.image} alt={member.name} className="w-full h-full object-cover" />
              </div>
              <div className="p-6">
                <h3 className="text-xl font-bold text-gray-900 mb-1">
                  {member.name}
                </h3>
                <p className="text-blue-600 font-medium mb-4">{member.role}</p>
                <p className="text-gray-600 text-sm">{member.bio}</p>
              </div>
            </motion.div>)}
        </motion.div>
      </div>
    </section>;
};
const Certifications = () => {
  const ref = useRef(null);
  const isInView = useInView(ref, {
    once: true,
    margin: '-100px'
  });
  const controls = useAnimation();
  useEffect(() => {
    if (isInView) {
      controls.start('visible');
    }
  }, [isInView, controls]);
  const containerVariants = {
    hidden: {
      opacity: 0
    },
    visible: {
      opacity: 1,
      transition: {
        staggerChildren: 0.15,
        delayChildren: 0.2
      }
    }
  };
  const itemVariants = {
    hidden: {
      y: 30,
      opacity: 0
    },
    visible: {
      y: 0,
      opacity: 1,
      transition: {
        duration: 0.5,
        ease: 'easeOut'
      }
    }
  };
  const certifications = ['ASE Master Automotive Technician', 'ASE Medium/Heavy Truck', 'ASE Heating & Air Conditioning', 'ASE Electrical/Electronic System', 'ASE Engine Repair', 'ASE Exhaust System', 'ASE Automatic Transmission/Transaxle', 'ASE Suspension & Steering', 'ASE Brakes', 'General Motors: Powertrain & Engine Management', 'Chrysler: Powertrain & Engine Management', 'Mercedes Benz: Powertrain & Engine Management', 'Ford Master Technician', 'Toyota Master Technician', 'Lexus Master Technician'];
  return <section ref={ref} className="py-20 bg-gradient-to-b from-[#1e3a5f] to-[#0f2542] text-white">
      <div className="container mx-auto px-4 md:px-8">
        <motion.div variants={containerVariants} initial="hidden" animate={controls} className="text-center max-w-3xl mx-auto mb-16">
          <motion.span variants={itemVariants} className="inline-block bg-blue-900 text-blue-100 px-4 py-1 rounded-full text-sm font-semibold mb-3">
            OUR EXPERTISE
          </motion.span>
          <motion.h2 variants={itemVariants} className="text-4xl font-bold text-white mb-4">
            Certifications & Expertise
          </motion.h2>
          <motion.p variants={itemVariants} className="text-lg text-blue-100">
            Our team holds numerous industry certifications, ensuring we have
            the knowledge and skills to service virtually any vehicle make and
            model.
          </motion.p>
        </motion.div>
        <motion.div variants={containerVariants} initial="hidden" animate={controls} className="grid grid-cols-1 md:grid-cols-3 gap-8 max-w-5xl mx-auto">
          <motion.div variants={itemVariants} className="md:col-span-2">
            <div className="bg-white/10 rounded-2xl p-8">
              <h3 className="text-2xl font-bold mb-6">Our Certifications</h3>
              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                {certifications.map((cert, index) => <div key={index} className="flex items-start">
                    <CheckCircleIcon className="h-5 w-5 text-blue-300 mr-3 mt-1 flex-shrink-0" />
                    <span>{cert}</span>
                  </div>)}
              </div>
            </div>
          </motion.div>
          <motion.div variants={itemVariants}>
            <div className="bg-white/10 rounded-2xl p-8 h-full">
              <h3 className="text-2xl font-bold mb-6">Veteran Owned</h3>
              <p className="text-blue-100 mb-6">
                ADJ Automotive is a federally registered veteran-owned small
                business, bringing military precision and attention to detail to
                automotive repair.
              </p>
              <div className="rounded-xl overflow-hidden">
                <img src="https://images.unsplash.com/photo-1529626455594-4ff0802cfb7e?ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D&auto=format&fit=crop&w=687&q=80" alt="Veteran Owned Business" className="w-full" />
              </div>
            </div>
          </motion.div>
        </motion.div>
      </div>
    </section>;
};
const Testimonials = () => {
  const ref = useRef(null);
  const isInView = useInView(ref, {
    once: true,
    margin: '-100px'
  });
  const controls = useAnimation();
  useEffect(() => {
    if (isInView) {
      controls.start('visible');
    }
  }, [isInView, controls]);
  const containerVariants = {
    hidden: {
      opacity: 0
    },
    visible: {
      opacity: 1,
      transition: {
        staggerChildren: 0.15,
        delayChildren: 0.2
      }
    }
  };
  const itemVariants = {
    hidden: {
      y: 30,
      opacity: 0
    },
    visible: {
      y: 0,
      opacity: 1,
      transition: {
        duration: 0.5,
        ease: 'easeOut'
      }
    }
  };
  const testimonials = [{
    name: 'Michael Rodriguez',
    role: 'Toyota Owner',
    image: 'https://images.unsplash.com/photo-1500648767791-00dcc994a43e?ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D&auto=format&fit=crop&w=387&q=80',
    text: 'ADJ Automotive rebuilt the transmission in my Toyota and it runs better than new. Their attention to detail and expertise is unmatched. Highly recommend their services!'
  }, {
    name: 'Sarah Johnson',
    role: 'Ford F-150 Owner',
    image: 'https://images.unsplash.com/photo-1494790108377-be9c29b29330?ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D&auto=format&fit=crop&w=387&q=80',
    text: "I brought my F-150 in with engine issues that two other shops couldn't diagnose. ADJ found and fixed the problem in one day. Their diagnostic equipment is top-notch!"
  }, {
    name: 'David Chen',
    role: 'Mercedes Owner',
    image: 'https://images.unsplash.com/photo-1507003211169-0a1dd7228f2d?ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D&auto=format&fit=crop&w=387&q=80',
    text: "As a Mercedes owner, I was worried about finding quality service outside the dealership. ADJ's master certified technicians provided exceptional service at half the dealership price."
  }];
  return <section ref={ref} className="py-20 bg-slate-100">
      <div className="container mx-auto px-4 md:px-8">
        <motion.div variants={containerVariants} initial="hidden" animate={controls} className="text-center max-w-3xl mx-auto mb-16">
          <motion.span variants={itemVariants} className="inline-block bg-blue-100 text-blue-800 px-4 py-1 rounded-full text-sm font-semibold mb-3">
            TESTIMONIALS
          </motion.span>
          <motion.h2 variants={itemVariants} className="text-4xl font-bold text-gray-900 mb-4">
            What Our Customers Say
          </motion.h2>
          <motion.p variants={itemVariants} className="text-lg text-gray-600">
            Don't just take our word for it. Here's what our satisfied customers
            have to say about their experience with ADJ Automotive.
          </motion.p>
        </motion.div>
        <motion.div variants={containerVariants} initial="hidden" animate={controls} className="grid grid-cols-1 md:grid-cols-3 gap-8">
          {testimonials.map((testimonial, index) => <motion.div key={index} initial={{
          y: 30,
          opacity: 0
        }} animate={{
          y: 0,
          opacity: 1,
          transition: {
            duration: 0.6,
            delay: index * 0.1,
            ease: 'easeOut'
          }
        }} whileHover={{
          y: -10,
          transition: {
            type: "spring",
            stiffness: 400,
            damping: 25
          }
        }} style={{
          transform: 'translateY(0px)'
        }} className="bg-white rounded-2xl p-8 shadow-xl">
              <div className="flex items-center mb-6">
                <div className="h-16 w-16 rounded-full overflow-hidden mr-4">
                  <img src={testimonial.image} alt={testimonial.name} className="w-full h-full object-cover" />
                </div>
                <div>
                  <h4 className="text-lg font-bold text-gray-900">
                    {testimonial.name}
                  </h4>
                  <p className="text-blue-600">{testimonial.role}</p>
                </div>
              </div>
              <p className="text-gray-600 italic">"{testimonial.text}"</p>
              <div className="mt-4 flex">
                {[...Array(5)].map((_, i) => <svg key={i} xmlns="http://www.w3.org/2000/svg" className="h-5 w-5 text-yellow-500" viewBox="0 0 20 20" fill="currentColor">
                    <path d="M9.049 2.927c.3-.921 1.603-.921 1.902 0l1.07 3.292a1 1 0 00.95.69h3.462c.969 0 1.371 1.24.588 1.81l-2.8 2.034a1 1 0 00-.364 1.118l1.07 3.292c.3.921-.755 1.688-1.54 1.118l-2.8-2.034a1 1 0 00-1.175 0l-2.8 2.034c-.784.57-1.838-.197-1.539-1.118l1.07-3.292a1 1 0 00-.364-1.118L2.98 8.72c-.783-.57-.38-1.81.588-1.81h3.461a1 1 0 00.951-.69l1.07-3.292z" />
                  </svg>)}
              </div>
            </motion.div>)}
        </motion.div>
      </div>
    </section>;
};
const CtaSection = () => {
  const ref = useRef(null);
  const isInView = useInView(ref, {
    once: true,
    margin: '-100px'
  });
  const controls = useAnimation();
  useEffect(() => {
    if (isInView) {
      controls.start('visible');
    }
  }, [isInView, controls]);
  const containerVariants = {
    hidden: {
      opacity: 0
    },
    visible: {
      opacity: 1,
      transition: {
        staggerChildren: 0.15,
        delayChildren: 0.2
      }
    }
  };
  const itemVariants = {
    hidden: {
      y: 30,
      opacity: 0
    },
    visible: {
      y: 0,
      opacity: 1,
      transition: {
        duration: 0.5,
        ease: 'easeOut'
      }
    }
  };
  return <section ref={ref} className="py-20 bg-white">
      <div className="container mx-auto px-4 md:px-8">
        <motion.div variants={containerVariants} initial="hidden" animate={controls} className="bg-[#1e3a5f] rounded-3xl p-8 md:p-16 shadow-2xl">
          <div className="grid grid-cols-1 md:grid-cols-2 gap-8 items-center">
            <div>
              <motion.span variants={itemVariants} className="inline-block bg-blue-800 text-blue-100 px-4 py-1 rounded-full text-sm font-semibold mb-3">
                EXPERIENCE THE DIFFERENCE
              </motion.span>
              <motion.h2 variants={itemVariants} className="text-3xl md:text-4xl font-bold text-white mb-4">
                Ready to experience dealership quality repair at an affordable
                price?
              </motion.h2>
              <motion.p variants={itemVariants} className="text-lg text-blue-100 mb-8">
                Whether you need transmission work, engine repair, or any
                automotive service, our team of certified experts is ready to
                help.
              </motion.p>
              <motion.div variants={itemVariants} className="flex flex-wrap gap-4">
                <Link to="/services#booking" className="bg-white hover:bg-gray-100 text-[#1e3a5f] px-8 py-3 rounded-full font-semibold text-lg flex items-center">
                  Book a Service <ArrowRightIcon className="ml-2 h-5 w-5" />
                </Link>
                <Link to="/contact" className="border-2 border-white text-white hover:bg-white hover:text-[#1e3a5f] px-8 py-3 rounded-full font-semibold text-lg transition-colors">
                  Contact Us
                </Link>
              </motion.div>
            </div>
            <motion.div variants={itemVariants} className="hidden md:block">
              <img src="https://images.unsplash.com/photo-1530046339160-ce3e530c7d2f?ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D&auto=format&fit=crop&w=1470&q=80" alt="Auto repair technician" className="rounded-xl shadow-lg w-full h-80 object-cover" />
            </motion.div>
          </div>
        </motion.div>
      </div>
    </section>;
};
export default About;