import { useEffect, useRef, useState } from 'react';
import { motion, AnimatePresence } from 'framer-motion';
import { ArrowRightIcon, ChevronLeftIcon, ChevronRightIcon } from 'lucide-react';
import { useNavigate } from 'react-router-dom';

const HeroSection = () => {
  const [currentSlide, setCurrentSlide] = useState(0);
  const [isAutoPlaying, setIsAutoPlaying] = useState(true);
  const [windowWidth, setWindowWidth] = useState(typeof window !== 'undefined' ? window.innerWidth : 1200);
  const autoPlayRef = useRef<NodeJS.Timeout | null>(null);
  const navigate = useNavigate();

  // Handler functions for button actions
  const handleBookService = () => {
    navigate('/book-service');
  };

  const handleViewCars = () => {
    navigate('/cars');
  };

  const handleTransmissionServices = () => {
    navigate('/services#transmission');
  };

  const handleDiagnosticServices = () => {
    navigate('/services#diagnostics');
  };

  const handlePhoneCall = () => {
    window.open('tel:+16714838335', '_self');
  };

  const slides = [
    {
      id: 1,
      backgroundImage: "url('/images/banners/hero-bg1.jpg')",
      backgroundPosition: 'center 60%',
      badge: "TRUSTED AUTOMOTIVE EXPERTS",
      title: "Dealership Quality Repair at an Affordable Price",
      description: "Specializing in transmission rebuilding, engine repair, and advanced diagnostics with over 38 years of combined experience.",
      primaryButton: "Book Appointment",
      primaryAction: handleBookService,
      secondaryButton: "View Cars for Sale",
      secondaryAction: handleViewCars
    },
    {
      id: 2,
      backgroundImage: "url('/images/banners/hero-bg2.jpg')",
      backgroundPosition: 'center 60%',
      badge: "EXPERT TRANSMISSION SERVICE",
      title: "Professional Transmission Rebuilding & Repair",
      description: "Complete transmission overhauls with 1-year labor warranty. From diagnostics to full rebuilds, we handle all transmission needs.",
      primaryButton: "Transmission Services",
      primaryAction: handleTransmissionServices,
      secondaryButton: "Get Free Quote",
      secondaryAction: handleBookService
    },
    {
      id: 3,
      backgroundImage: "url('/images/banners/hero-bg3.jpg')",
      backgroundPosition: 'center 60%',
      badge: "ADVANCED DIAGNOSTICS",
      title: "State-of-the-Art Diagnostic Equipment",
      description: "Using the latest Autel MaxiSys Ultra scan tool to diagnose and resolve even the most complex vehicle issues quickly and accurately.",
      primaryButton: "Diagnostic Services",
      primaryAction: handleDiagnosticServices,
      secondaryButton: "Call (*************",
      secondaryAction: handlePhoneCall
    }
  ];

  const nextSlide = () => {
    setCurrentSlide((prev) => (prev + 1) % slides.length);
  };

  const prevSlide = () => {
    setCurrentSlide((prev) => (prev - 1 + slides.length) % slides.length);
  };

  const goToSlide = (index: number) => {
    setCurrentSlide(index);
  };

  // Window resize listener
  useEffect(() => {
    const handleResize = () => {
      setWindowWidth(window.innerWidth);
    };

    window.addEventListener('resize', handleResize);
    return () => window.removeEventListener('resize', handleResize);
  }, []);

  // Auto-play functionality
  useEffect(() => {
    if (isAutoPlaying) {
      autoPlayRef.current = setInterval(() => {
        nextSlide();
      }, 5000); // Change slide every 5 seconds
    }

    return () => {
      if (autoPlayRef.current) {
        clearInterval(autoPlayRef.current);
      }
    };
  }, [isAutoPlaying, currentSlide]);

  // Pause auto-play on hover
  const handleMouseEnter = () => setIsAutoPlaying(false);
  const handleMouseLeave = () => setIsAutoPlaying(true);

  // Calculate responsive background position based on screen width
  const getBackgroundPosition = () => {
    if (windowWidth >= 1920) return 'right 50%';
    if (windowWidth >= 1440) return '90% 50%';
    if (windowWidth >= 1024) return 'center 55%';
    return 'center 60%';
  };

  const slideVariants = {
    enter: {
      opacity: 0,
      scale: 1.1
    },
    center: {
      opacity: 1,
      scale: 1,
      transition: {
        duration: 0.8,
        ease: 'easeOut'
      }
    },
    exit: {
      opacity: 0,
      scale: 0.9,
      transition: {
        duration: 0.5,
        ease: 'easeIn'
      }
    }
  };

  const contentVariants = {
    hidden: {
      opacity: 0,
      y: 30
    },
    visible: {
      opacity: 1,
      y: 0,
      transition: {
        duration: 0.6,
        delay: 0.3,
        ease: 'easeOut'
      }
    }
  };

  return (
    <section 
      className="relative w-full min-h-screen overflow-hidden" 
      style={{ height: '100vh' }}
      onMouseEnter={handleMouseEnter}
      onMouseLeave={handleMouseLeave}
    >
      {/* Carousel Slides */}
      <AnimatePresence mode="wait">
        <motion.div
          key={currentSlide}
          className="absolute inset-0 z-0 w-full h-full bg-cover bg-no-repeat"
          variants={slideVariants}
          initial="enter"
          animate="center"
          exit="exit"
          style={{
            backgroundImage: slides[currentSlide].backgroundImage,
            backgroundPosition: getBackgroundPosition()
          }}
        >
          {/* Overlay for better text readability */}
          <div 
            className="absolute inset-0 z-10" 
            style={{
              background: 'linear-gradient(to bottom, rgba(0,0,0,0.2), rgba(0,0,0,0.7))'
            }}
          ></div>
        </motion.div>
      </AnimatePresence>

      {/* Content */}
      <div className="absolute inset-0 z-30">
        <div className="container mx-auto h-full flex items-center px-4 md:px-8 py-20 md:py-0">
          <AnimatePresence mode="wait">
            <motion.div 
              key={currentSlide}
              className="max-w-3xl" 
              variants={contentVariants}
              initial="hidden"
              animate="visible"
              exit="hidden"
            >
              <motion.div className="mb-2">
                <span className="inline-block bg-blue-600 text-white px-4 py-1 rounded-full text-sm font-semibold mb-3">
                  {slides[currentSlide].badge}
                </span>
              </motion.div>
              <motion.h1 
                className="text-3xl sm:text-4xl md:text-5xl lg:text-6xl font-bold text-white mb-4 md:mb-6 leading-tight"
              >
                {slides[currentSlide].title}
              </motion.h1>
              <motion.p 
                className="text-base sm:text-lg md:text-xl text-gray-200 mb-6 md:mb-8 leading-relaxed"
              >
                {slides[currentSlide].description}
              </motion.p>
              <motion.div className="flex flex-col sm:flex-row gap-3 sm:gap-4">
                <motion.div
                  whileHover={{ scale: 1.05 }} 
                  whileTap={{ scale: 0.95 }} 
                  transition={{
                    type: 'spring',
                    stiffness: 400,
                    damping: 17
                  }}
                >
                  <button 
                    onClick={slides[currentSlide].primaryAction}
                    className="bg-blue-600 hover:bg-blue-700 text-white px-6 sm:px-8 py-3 rounded-full font-semibold text-base sm:text-lg flex items-center justify-center w-full sm:w-auto transition-colors" 
                  >
                    {slides[currentSlide].primaryButton} <ArrowRightIcon className="ml-2 h-4 w-4 sm:h-5 sm:w-5" />
                  </button>
                </motion.div>
                <motion.div
                  whileHover={{ scale: 1.05 }} 
                  whileTap={{ scale: 0.95 }} 
                  transition={{
                    type: 'spring',
                    stiffness: 400,
                    damping: 17
                  }}
                >
                  <button 
                    onClick={slides[currentSlide].secondaryAction}
                    className="bg-white hover:bg-gray-100 text-[#1e3a5f] px-6 sm:px-8 py-3 rounded-full font-semibold text-base sm:text-lg w-full sm:w-auto transition-colors" 
                  >
                    {slides[currentSlide].secondaryButton}
                  </button>
                </motion.div>
              </motion.div>
            </motion.div>
          </AnimatePresence>
        </div>
      </div>

      {/* Navigation Controls - Hidden on mobile */}
      {/* Previous Button */}
      <motion.button
        onClick={prevSlide}
        className="hidden md:block absolute left-4 md:left-8 top-1/2 transform -translate-y-1/2 z-40 bg-white/20 hover:bg-white/30 text-white p-3 rounded-full backdrop-blur-sm transition-colors"
        whileHover={{ scale: 1.1 }}
        whileTap={{ scale: 0.9 }}
      >
        <ChevronLeftIcon className="h-6 w-6" />
      </motion.button>

      {/* Next Button */}
      <motion.button
        onClick={nextSlide}
        className="hidden md:block absolute right-4 md:right-8 top-1/2 transform -translate-y-1/2 z-40 bg-white/20 hover:bg-white/30 text-white p-3 rounded-full backdrop-blur-sm transition-colors"
        whileHover={{ scale: 1.1 }}
        whileTap={{ scale: 0.9 }}
      >
        <ChevronRightIcon className="h-6 w-6" />
      </motion.button>

      {/* Slide Indicators */}
      <div className="absolute bottom-8 left-1/2 transform -translate-x-1/2 z-30 flex space-x-3">
        {slides.map((_, index) => (
          <motion.button
            key={index}
            onClick={() => goToSlide(index)}
            className={`w-3 h-3 rounded-full transition-all duration-300 pointer-events-auto ${
              index === currentSlide 
                ? 'bg-white scale-125' 
                : 'bg-white/50 hover:bg-white/75'
            }`}
            whileHover={{ scale: 1.2 }}
            whileTap={{ scale: 0.9 }}
          />
        ))}
      </div>
    </section>
  );
};

export default HeroSection;