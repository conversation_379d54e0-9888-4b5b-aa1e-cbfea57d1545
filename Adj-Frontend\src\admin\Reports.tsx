import React, { useState } from 'react';
import { motion } from 'framer-motion';
import { LayoutDashboardIcon, CarIcon, ClipboardListIcon, UsersIcon, SettingsIcon, LogOutIcon, FileTextIcon, DownloadIcon, CalendarIcon, TrendingUpIcon, DollarSignIcon, FilterIcon, ChevronDownIcon, BarChart3Icon, PieChartIcon, LineChartIcon } from 'lucide-react';
import { Link } from 'react-router-dom';
import AdminSidebar from '../components/AdminSidebar';
const Reports = () => {
  const [sidebarOpen, setSidebarOpen] = useState(false);
  const [reportType, setReportType] = useState('sales');
  const [dateRange, setDateRange] = useState('month');
  const toggleSidebar = () => {
    setSidebarOpen(!sidebarOpen);
  };
  return <div className="flex h-screen bg-gray-100">
      {/* Sidebar for desktop */}
      <aside className="bg-[#1e3a5f] text-white w-64 flex-shrink-0 hidden md:block">
        <AdminSidebar activePage="reports" />
      </aside>
      {/* Mobile sidebar */}
      <div className={`fixed inset-0 bg-gray-800 bg-opacity-75 z-40 md:hidden transition-opacity duration-200 ${sidebarOpen ? 'opacity-100' : 'opacity-0 pointer-events-none'}`} onClick={toggleSidebar}></div>
      <aside className={`fixed top-0 left-0 h-full bg-[#1e3a5f] text-white w-64 z-50 transform transition-transform duration-200 ease-in-out md:hidden ${sidebarOpen ? 'translate-x-0' : '-translate-x-full'}`}>
        <div className="absolute top-4 right-4">
          <button className="text-white p-2" onClick={toggleSidebar}>
            <span className="sr-only">Close sidebar</span>
            <svg className="h-6 w-6" fill="none" viewBox="0 0 24 24" stroke="currentColor">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M6 18L18 6M6 6l12 12" />
            </svg>
          </button>
        </div>
        <AdminSidebar activePage="reports" />
      </aside>
      {/* Main content */}
      <div className="flex-1 flex flex-col overflow-hidden">
        {/* Top header */}
        <header className="bg-white shadow-sm z-10">
          <div className="px-4 py-4 flex justify-between items-center">
            <div className="flex items-center md:hidden">
              <button className="text-gray-600 p-2" onClick={toggleSidebar}>
                <span className="sr-only">Open sidebar</span>
                <svg className="h-6 w-6" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M4 6h16M4 12h16M4 18h16" />
                </svg>
              </button>
            </div>
            <h1 className="text-xl font-semibold text-gray-800">Reports</h1>
            <div>
              <button className="flex items-center px-4 py-2 border border-gray-300 rounded-lg text-gray-700 hover:bg-gray-50">
                <DownloadIcon className="h-4 w-4 mr-2" />
                Export
              </button>
            </div>
          </div>
        </header>
        {/* Main content area */}
        <main className="flex-1 overflow-y-auto bg-gray-100 p-4">
          <div className="space-y-6">
            {/* Report Controls */}
            <div className="bg-white rounded-xl shadow-sm p-4">
              <div className="flex flex-col md:flex-row md:items-center justify-between gap-4">
                <div className="flex flex-wrap gap-2">
                  <button className={`px-4 py-2 rounded-lg ${reportType === 'sales' ? 'bg-[#1e3a5f] text-white' : 'bg-gray-100 text-gray-700 hover:bg-gray-200'}`} onClick={() => setReportType('sales')}>
                    Sales
                  </button>
                  <button className={`px-4 py-2 rounded-lg ${reportType === 'service' ? 'bg-[#1e3a5f] text-white' : 'bg-gray-100 text-gray-700 hover:bg-gray-200'}`} onClick={() => setReportType('service')}>
                    Service
                  </button>
                  <button className={`px-4 py-2 rounded-lg ${reportType === 'inventory' ? 'bg-[#1e3a5f] text-white' : 'bg-gray-100 text-gray-700 hover:bg-gray-200'}`} onClick={() => setReportType('inventory')}>
                    Inventory
                  </button>
                  <button className={`px-4 py-2 rounded-lg ${reportType === 'customer' ? 'bg-[#1e3a5f] text-white' : 'bg-gray-100 text-gray-700 hover:bg-gray-200'}`} onClick={() => setReportType('customer')}>
                    Customer
                  </button>
                </div>
                <div className="flex items-center space-x-4">
                  <select value={dateRange} onChange={e => setDateRange(e.target.value)} className="px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent">
                    <option value="week">This Week</option>
                    <option value="month">This Month</option>
                    <option value="quarter">This Quarter</option>
                    <option value="year">This Year</option>
                    <option value="custom">Custom Range</option>
                  </select>
                  <button className="p-2 text-gray-600 hover:text-gray-800">
                    <FilterIcon className="h-5 w-5" />
                  </button>
                </div>
              </div>
            </div>
            {/* Summary Cards */}
            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
              <motion.div className="bg-white rounded-xl shadow-sm p-6" initial={{
              opacity: 0,
              y: 20
            }} animate={{
              opacity: 1,
              y: 0
            }} transition={{
              delay: 0.1
            }}>
                <div className="flex items-center justify-between">
                  <div>
                    <p className="text-sm font-medium text-gray-500">
                      {reportType === 'sales' ? 'Total Sales' : reportType === 'service' ? 'Service Revenue' : reportType === 'inventory' ? 'Inventory Value' : 'Total Customers'}
                    </p>
                    <p className="text-2xl font-bold text-gray-900 mt-1">
                      {reportType === 'sales' ? '$148,290' : reportType === 'service' ? '$42,380' : reportType === 'inventory' ? '$1.2M' : '278'}
                    </p>
                  </div>
                  <div className="bg-blue-500 text-white p-3 rounded-lg">
                    {reportType === 'sales' || reportType === 'service' ? <DollarSignIcon className="h-6 w-6" /> : reportType === 'inventory' ? <CarIcon className="h-6 w-6" /> : <UsersIcon className="h-6 w-6" />}
                  </div>
                </div>
                <div className="mt-4 flex items-center">
                  <span className="text-green-600 text-sm font-medium flex items-center">
                    <TrendingUpIcon className="h-4 w-4 mr-1" />
                    {reportType === 'sales' ? '+12%' : reportType === 'service' ? '+8%' : reportType === 'inventory' ? '+3%' : '+18%'}
                  </span>
                  <span className="text-gray-500 text-sm ml-2">
                    from last period
                  </span>
                </div>
              </motion.div>
              <motion.div className="bg-white rounded-xl shadow-sm p-6" initial={{
              opacity: 0,
              y: 20
            }} animate={{
              opacity: 1,
              y: 0
            }} transition={{
              delay: 0.2
            }}>
                <div className="flex items-center justify-between">
                  <div>
                    <p className="text-sm font-medium text-gray-500">
                      {reportType === 'sales' ? 'Units Sold' : reportType === 'service' ? 'Service Orders' : reportType === 'inventory' ? 'Total Units' : 'New Customers'}
                    </p>
                    <p className="text-2xl font-bold text-gray-900 mt-1">
                      {reportType === 'sales' ? '18' : reportType === 'service' ? '124' : reportType === 'inventory' ? '36' : '42'}
                    </p>
                  </div>
                  <div className="bg-green-500 text-white p-3 rounded-lg">
                    {reportType === 'sales' ? <BarChart3Icon className="h-6 w-6" /> : reportType === 'service' ? <ClipboardListIcon className="h-6 w-6" /> : reportType === 'inventory' ? <PieChartIcon className="h-6 w-6" /> : <TrendingUpIcon className="h-6 w-6" />}
                  </div>
                </div>
                <div className="mt-4 flex items-center">
                  <span className={`text-sm font-medium flex items-center ${reportType === 'inventory' ? 'text-red-600' : 'text-green-600'}`}>
                    {reportType === 'inventory' ? <>
                        <svg xmlns="http://www.w3.org/2000/svg" className="h-4 w-4 mr-1" viewBox="0 0 20 20" fill="currentColor">
                          <path fillRule="evenodd" d="M16.707 10.293a1 1 0 010 1.414l-6 6a1 1 0 01-1.414 0l-6-6a1 1 0 111.414-1.414L9 14.586V3a1 1 0 012 0v11.586l4.293-4.293a1 1 0 011.414 0z" clipRule="evenodd" />
                        </svg>
                        -5%
                      </> : <>
                        <TrendingUpIcon className="h-4 w-4 mr-1" />
                        {reportType === 'sales' ? '+22%' : reportType === 'service' ? '+15%' : '+32%'}
                      </>}
                  </span>
                  <span className="text-gray-500 text-sm ml-2">
                    from last period
                  </span>
                </div>
              </motion.div>
              <motion.div className="bg-white rounded-xl shadow-sm p-6" initial={{
              opacity: 0,
              y: 20
            }} animate={{
              opacity: 1,
              y: 0
            }} transition={{
              delay: 0.3
            }}>
                <div className="flex items-center justify-between">
                  <div>
                    <p className="text-sm font-medium text-gray-500">
                      {reportType === 'sales' ? 'Average Sale Price' : reportType === 'service' ? 'Average Service Value' : reportType === 'inventory' ? 'Avg Days in Inventory' : 'Customer Retention'}
                    </p>
                    <p className="text-2xl font-bold text-gray-900 mt-1">
                      {reportType === 'sales' ? '$28,490' : reportType === 'service' ? '$342' : reportType === 'inventory' ? '48' : '78%'}
                    </p>
                  </div>
                  <div className="bg-purple-500 text-white p-3 rounded-lg">
                    {reportType === 'sales' || reportType === 'service' ? <LineChartIcon className="h-6 w-6" /> : reportType === 'inventory' ? <CalendarIcon className="h-6 w-6" /> : <UsersIcon className="h-6 w-6" />}
                  </div>
                </div>
                <div className="mt-4 flex items-center">
                  <span className={`text-sm font-medium flex items-center ${reportType === 'sales' ? 'text-red-600' : 'text-green-600'}`}>
                    {reportType === 'sales' ? <>
                        <svg xmlns="http://www.w3.org/2000/svg" className="h-4 w-4 mr-1" viewBox="0 0 20 20" fill="currentColor">
                          <path fillRule="evenodd" d="M16.707 10.293a1 1 0 010 1.414l-6 6a1 1 0 01-1.414 0l-6-6a1 1 0 111.414-1.414L9 14.586V3a1 1 0 012 0v11.586l4.293-4.293a1 1 0 011.414 0z" clipRule="evenodd" />
                        </svg>
                        -3%
                      </> : <>
                        <TrendingUpIcon className="h-4 w-4 mr-1" />
                        {reportType === 'service' ? '+5%' : reportType === 'inventory' ? '+10%' : '+4%'}
                      </>}
                  </span>
                  <span className="text-gray-500 text-sm ml-2">
                    from last period
                  </span>
                </div>
              </motion.div>
              <motion.div className="bg-white rounded-xl shadow-sm p-6" initial={{
              opacity: 0,
              y: 20
            }} animate={{
              opacity: 1,
              y: 0
            }} transition={{
              delay: 0.4
            }}>
                <div className="flex items-center justify-between">
                  <div>
                    <p className="text-sm font-medium text-gray-500">
                      {reportType === 'sales' ? 'Profit Margin' : reportType === 'service' ? 'Technician Efficiency' : reportType === 'inventory' ? 'Inventory Turnover' : 'Satisfaction Rate'}
                    </p>
                    <p className="text-2xl font-bold text-gray-900 mt-1">
                      {reportType === 'sales' ? '18.5%' : reportType === 'service' ? '92%' : reportType === 'inventory' ? '3.8x' : '4.8/5'}
                    </p>
                  </div>
                  <div className="bg-yellow-500 text-white p-3 rounded-lg">
                    <TrendingUpIcon className="h-6 w-6" />
                  </div>
                </div>
                <div className="mt-4 flex items-center">
                  <span className="text-green-600 text-sm font-medium flex items-center">
                    <TrendingUpIcon className="h-4 w-4 mr-1" />
                    {reportType === 'sales' ? '+2.5%' : reportType === 'service' ? '+3%' : reportType === 'inventory' ? '+0.4x' : '+0.2'}
                  </span>
                  <span className="text-gray-500 text-sm ml-2">
                    from last period
                  </span>
                </div>
              </motion.div>
            </div>
            {/* Main Chart */}
            <motion.div className="bg-white rounded-xl shadow-sm p-6" initial={{
            opacity: 0,
            y: 20
          }} animate={{
            opacity: 1,
            y: 0
          }} transition={{
            delay: 0.5
          }}>
              <div className="flex items-center justify-between mb-6">
                <h2 className="text-lg font-semibold text-gray-900">
                  {reportType === 'sales' ? 'Sales Performance' : reportType === 'service' ? 'Service Revenue' : reportType === 'inventory' ? 'Inventory Levels' : 'Customer Growth'}
                </h2>
                <div className="flex items-center">
                  <button className="flex items-center text-sm text-gray-600 hover:text-gray-900">
                    <span className="mr-1">More Details</span>
                    <ChevronDownIcon className="h-4 w-4" />
                  </button>
                </div>
              </div>
              {/* Chart Placeholder */}
              <div className="h-80 w-full">
                <div className="h-full w-full flex items-end justify-between px-4 space-x-2">
                  {[65, 59, 80, 81, 56, 55, 72, 60, 76, 85, 65, 75].map((height, index) => <div key={index} className="flex-1 flex flex-col items-center">
                        <div className="w-full bg-blue-500 rounded-t-sm transition-all duration-1000" style={{
                    height: `${height}%`
                  }}></div>
                        <div className="text-xs text-gray-500 mt-2">
                          {['Jan', 'Feb', 'Mar', 'Apr', 'May', 'Jun', 'Jul', 'Aug', 'Sep', 'Oct', 'Nov', 'Dec'][index]}
                        </div>
                      </div>)}
                </div>
              </div>
            </motion.div>
            {/* Additional Charts/Tables based on report type */}
            <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
              <motion.div className="bg-white rounded-xl shadow-sm p-6" initial={{
              opacity: 0,
              y: 20
            }} animate={{
              opacity: 1,
              y: 0
            }} transition={{
              delay: 0.6
            }}>
                <h2 className="text-lg font-semibold text-gray-900 mb-4">
                  {reportType === 'sales' ? 'Top Selling Vehicles' : reportType === 'service' ? 'Service Type Distribution' : reportType === 'inventory' ? 'Stock Status' : 'Customer Demographics'}
                </h2>
                {/* Pie Chart Placeholder */}
                <div className="flex items-center justify-center h-64">
                  <div className="relative h-48 w-48">
                    <div className="absolute inset-0 rounded-full border-8 border-t-blue-500 border-r-purple-500 border-b-green-500 border-l-yellow-500 transform rotate-45"></div>
                    <div className="absolute inset-4 bg-white rounded-full flex items-center justify-center">
                      <div className="text-center">
                        <div className="text-2xl font-bold text-gray-800">
                          {reportType === 'sales' ? '18' : reportType === 'service' ? '124' : reportType === 'inventory' ? '36' : '278'}
                        </div>
                        <div className="text-xs text-gray-500">
                          {reportType === 'sales' ? 'Total Vehicles' : reportType === 'service' ? 'Total Services' : reportType === 'inventory' ? 'Total Units' : 'Total Customers'}
                        </div>
                      </div>
                    </div>
                  </div>
                  <div className="ml-8 space-y-3">
                    <div className="flex items-center">
                      <div className="h-3 w-3 bg-blue-500 rounded-full mr-2"></div>
                      <span className="text-sm text-gray-600">
                        {reportType === 'sales' ? 'SUVs (42%)' : reportType === 'service' ? 'Transmission (42%)' : reportType === 'inventory' ? 'Available (60%)' : 'Male (56%)'}
                      </span>
                    </div>
                    <div className="flex items-center">
                      <div className="h-3 w-3 bg-purple-500 rounded-full mr-2"></div>
                      <span className="text-sm text-gray-600">
                        {reportType === 'sales' ? 'Sedans (28%)' : reportType === 'service' ? 'Engine (28%)' : reportType === 'inventory' ? 'Pending (25%)' : 'Female (42%)'}
                      </span>
                    </div>
                    <div className="flex items-center">
                      <div className="h-3 w-3 bg-green-500 rounded-full mr-2"></div>
                      <span className="text-sm text-gray-600">
                        {reportType === 'sales' ? 'Trucks (18%)' : reportType === 'service' ? 'Diagnostics (18%)' : reportType === 'inventory' ? 'Sold (10%)' : 'Other (2%)'}
                      </span>
                    </div>
                    <div className="flex items-center">
                      <div className="h-3 w-3 bg-yellow-500 rounded-full mr-2"></div>
                      <span className="text-sm text-gray-600">
                        {reportType === 'sales' ? 'Sports (12%)' : reportType === 'service' ? 'Other (12%)' : reportType === 'inventory' ? 'Reserved (5%)' : 'Unknown (0%)'}
                      </span>
                    </div>
                  </div>
                </div>
              </motion.div>
              <motion.div className="bg-white rounded-xl shadow-sm p-6" initial={{
              opacity: 0,
              y: 20
            }} animate={{
              opacity: 1,
              y: 0
            }} transition={{
              delay: 0.7
            }}>
                <h2 className="text-lg font-semibold text-gray-900 mb-4">
                  {reportType === 'sales' ? 'Sales by Source' : reportType === 'service' ? 'Revenue by Technician' : reportType === 'inventory' ? 'Age of Inventory' : 'Customer Acquisition'}
                </h2>
                <div className="overflow-x-auto">
                  <table className="min-w-full">
                    <thead>
                      <tr className="bg-gray-50 border-b border-gray-200">
                        <th className="px-4 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                          {reportType === 'sales' ? 'Source' : reportType === 'service' ? 'Technician' : reportType === 'inventory' ? 'Time Period' : 'Channel'}
                        </th>
                        <th className="px-4 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                          {reportType === 'sales' || reportType === 'service' ? 'Revenue' : reportType === 'inventory' ? 'Units' : 'Customers'}
                        </th>
                        <th className="px-4 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                          {reportType === 'sales' ? 'Units' : reportType === 'service' ? 'Orders' : reportType === 'inventory' ? '% of Total' : 'Conversion'}
                        </th>
                        <th className="px-4 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                          Trend
                        </th>
                      </tr>
                    </thead>
                    <tbody className="divide-y divide-gray-200">
                      {[1, 2, 3, 4, 5].map((item, index) => <tr key={index} className="hover:bg-gray-50">
                          <td className="px-4 py-3 whitespace-nowrap text-sm font-medium text-gray-900">
                            {reportType === 'sales' ? ['Website', 'Walk-in', 'Referral', 'Social Media', 'Auto Trader'][index] : reportType === 'service' ? ['David Chen', 'Sarah Williams', 'Adam Johnson', 'Michael Chen', 'Jennifer Lee'][index] : reportType === 'inventory' ? ['< 30 days', '30-60 days', '60-90 days', '90-120 days', '> 120 days'][index] : ['Organic Search', 'Direct', 'Social Media', 'Referral', 'Paid Ads'][index]}
                          </td>
                          <td className="px-4 py-3 whitespace-nowrap text-sm text-gray-900">
                            {reportType === 'sales' ? ['$58,450', '$42,890', '$24,650', '$14,300', '$8,000'][index] : reportType === 'service' ? ['$12,450', '$9,890', '$8,650', '$6,300', '$5,090'][index] : reportType === 'inventory' ? ['14', '8', '6', '5', '3'][index] : ['124', '58', '42', '36', '18'][index]}
                          </td>
                          <td className="px-4 py-3 whitespace-nowrap text-sm text-gray-900">
                            {reportType === 'sales' ? ['7', '5', '3', '2', '1'][index] : reportType === 'service' ? ['38', '32', '26', '18', '10'][index] : reportType === 'inventory' ? ['38.9%', '22.2%', '16.7%', '13.9%', '8.3%'][index] : ['8.2%', '12.4%', '6.8%', '15.2%', '4.5%'][index]}
                          </td>
                          <td className="px-4 py-3 whitespace-nowrap">
                            <div className={`text-${index % 3 === 0 ? 'green' : index % 3 === 1 ? 'yellow' : 'red'}-600`}>
                              {index % 3 === 0 ? <TrendingUpIcon className="h-5 w-5" /> : index % 3 === 1 ? <svg xmlns="http://www.w3.org/2000/svg" className="h-5 w-5" viewBox="0 0 20 20" fill="currentColor">
                                  <path fillRule="evenodd" d="M7.707 10.293a1 1 0 100 1.414l3 3a1 1 0 001.414 0l3-3a1 1 0 00-1.414-1.414L11 12.586V6a1 1 0 10-2 0v6.586l-1.293-1.293a1 1 0 00-1.414 0z" clipRule="evenodd" />
                                </svg> : <svg xmlns="http://www.w3.org/2000/svg" className="h-5 w-5" viewBox="0 0 20 20" fill="currentColor">
                                  <path fillRule="evenodd" d="M16.707 10.293a1 1 0 010 1.414l-6 6a1 1 0 01-1.414 0l-6-6a1 1 0 111.414-1.414L9 14.586V3a1 1 0 012 0v11.586l4.293-4.293a1 1 0 011.414 0z" clipRule="evenodd" />
                                </svg>}
                            </div>
                          </td>
                        </tr>)}
                    </tbody>
                  </table>
                </div>
              </motion.div>
            </div>
          </div>
        </main>
      </div>
    </div>;
};
export default Reports;