import React, { useState } from 'react';
import { motion } from 'framer-motion';
import ServiceForm from '../components/ServiceForm';

const BookService = () => {
  const containerVariants = {
    hidden: { opacity: 0 },
    visible: {
      opacity: 1,
      transition: { staggerChildren: 0.2 }
    }
  };

  const itemVariants = {
    hidden: { y: 30, opacity: 0 },
    visible: {
      y: 0,
      opacity: 1,
      transition: { duration: 0.6, ease: 'easeOut' }
    }
  };

  return (
    <main className="w-full">
      {/* Hero Section */}
      <section className="relative py-20 bg-[#1e3a5f] overflow-hidden">
        <div className="absolute inset-0 z-0">
          <div className="absolute inset-0 bg-black/50 z-10" style={{
            backgroundImage: 'linear-gradient(to bottom, rgba(30,58,95,0.8), rgba(15,37,66,0.95))'
          }}></div>
          <div className="w-full h-full bg-cover bg-center" style={{
            backgroundImage: "url('https://images.unsplash.com/photo-1619642751034-765dfdf7c58e?ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D&auto=format&fit=crop&w=1335&q=80')"
          }}></div>
        </div>
        <div className="container mx-auto px-4 md:px-8 relative z-10">
          <motion.div 
            className="max-w-3xl mx-auto text-center" 
            variants={containerVariants} 
            initial="hidden" 
            animate="visible"
          >
            <motion.span 
              variants={itemVariants} 
              className="inline-block bg-blue-700 text-white px-4 py-1 rounded-full text-sm font-semibold mb-3"
            >
              BOOK SERVICE
            </motion.span>
            <motion.h1 
              variants={itemVariants} 
              className="text-3xl sm:text-4xl md:text-5xl lg:text-6xl font-bold text-white mb-4 md:mb-6 leading-tight"
            >
              Book Your Service Appointment
            </motion.h1>
            <motion.p 
              variants={itemVariants} 
              className="text-base sm:text-lg md:text-xl text-blue-100 mb-6 md:mb-8 leading-relaxed"
            >
              Schedule your vehicle service with our expert technicians and get back on the road safely.
            </motion.p>
          </motion.div>
        </div>
      </section>

      {/* Form Section */}
      <section className="py-12 sm:py-16 md:py-20 bg-slate-100">
        <div className="container mx-auto px-4 md:px-8">
          <motion.div 
            className="max-w-4xl mx-auto"
            initial={{ opacity: 0, y: 30 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.6, delay: 0.3 }}
          >
            <ServiceForm />
          </motion.div>
        </div>
      </section>
    </main>
  );
};
export default BookService;