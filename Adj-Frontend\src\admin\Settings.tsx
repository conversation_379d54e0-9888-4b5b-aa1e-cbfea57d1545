import React, { useState } from 'react';
import { motion } from 'framer-motion';
import { LayoutDashboardIcon, CarIcon, ClipboardListIcon, UsersIcon, SettingsIcon, LogOutIcon, FileTextIcon, SaveIcon, UserIcon, BuildingIcon, GlobeIcon, BellIcon, LockIcon, CreditCardIcon, PaletteIcon, MailIcon, ServerIcon } from 'lucide-react';
import { Link } from 'react-router-dom';
import AdminSidebar from '../components/AdminSidebar';
const Settings = () => {
  const [sidebarOpen, setSidebarOpen] = useState(false);
  const [activeTab, setActiveTab] = useState('general');
  const [formData, setFormData] = useState({
    businessName: 'ADJ Automotive',
    email: '<EMAIL>',
    phone: '(*************',
    address: '123 Main Street',
    city: 'Anytown',
    state: 'CA',
    zipCode: '90210',
    website: 'www.adjauto.com',
    taxRate: '7.25',
    currency: 'USD',
    dateFormat: 'MM/DD/YYYY',
    timeZone: 'America/Los_Angeles',
    logoUrl: 'https://example.com/logo.png',
    enableNotifications: true,
    emailNotifications: true,
    smsNotifications: false
  });
  const toggleSidebar = () => {
    setSidebarOpen(!sidebarOpen);
  };
  const handleChange = e => {
    const {
      name,
      value,
      type,
      checked
    } = e.target;
    setFormData({
      ...formData,
      [name]: type === 'checkbox' ? checked : value
    });
  };
  const handleSubmit = e => {
    e.preventDefault();
    // In a real app, this would send data to your backend
    console.log('Settings updated:', formData);
    alert('Settings saved successfully!');
  };
  return <div className="flex h-screen bg-gray-100">
      {/* Sidebar for desktop */}
      <aside className="bg-[#1e3a5f] text-white w-64 flex-shrink-0 hidden md:block">
        <AdminSidebar activePage="settings" />
      </aside>
      {/* Mobile sidebar */}
      <div className={`fixed inset-0 bg-gray-800 bg-opacity-75 z-40 md:hidden transition-opacity duration-200 ${sidebarOpen ? 'opacity-100' : 'opacity-0 pointer-events-none'}`} onClick={toggleSidebar}></div>
      <aside className={`fixed top-0 left-0 h-full bg-[#1e3a5f] text-white w-64 z-50 transform transition-transform duration-200 ease-in-out md:hidden ${sidebarOpen ? 'translate-x-0' : '-translate-x-full'}`}>
        <div className="absolute top-4 right-4">
          <button className="text-white p-2" onClick={toggleSidebar}>
            <span className="sr-only">Close sidebar</span>
            <svg className="h-6 w-6" fill="none" viewBox="0 0 24 24" stroke="currentColor">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M6 18L18 6M6 6l12 12" />
            </svg>
          </button>
        </div>
        <AdminSidebar activePage="settings" />
      </aside>
      {/* Main content */}
      <div className="flex-1 flex flex-col overflow-hidden">
        {/* Top header */}
        <header className="bg-white shadow-sm z-10">
          <div className="px-4 py-4 flex justify-between items-center">
            <div className="flex items-center md:hidden">
              <button className="text-gray-600 p-2" onClick={toggleSidebar}>
                <span className="sr-only">Open sidebar</span>
                <svg className="h-6 w-6" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M4 6h16M4 12h16M4 18h16" />
                </svg>
              </button>
            </div>
            <h1 className="text-xl font-semibold text-gray-800">Settings</h1>
            <div>
              <button onClick={handleSubmit} className="flex items-center px-4 py-2 bg-[#1e3a5f] text-white rounded-lg hover:bg-blue-800 transition-colors">
                <SaveIcon className="h-4 w-4 mr-2" />
                Save Changes
              </button>
            </div>
          </div>
        </header>
        {/* Main content area */}
        <main className="flex-1 overflow-y-auto bg-gray-100 p-4">
          <div className="max-w-7xl mx-auto">
            <div className="flex flex-col md:flex-row gap-6">
              {/* Settings navigation */}
              <div className="md:w-64 flex-shrink-0">
                <div className="bg-white rounded-xl shadow-sm overflow-hidden">
                  <nav className="p-4 space-y-1">
                    <button onClick={() => setActiveTab('general')} className={`w-full flex items-center px-4 py-3 rounded-lg transition-colors ${activeTab === 'general' ? 'bg-blue-50 text-[#1e3a5f]' : 'text-gray-700 hover:bg-gray-50'}`}>
                      <BuildingIcon className="h-5 w-5 mr-3" />
                      <span className="font-medium">Business Information</span>
                    </button>
                    <button onClick={() => setActiveTab('users')} className={`w-full flex items-center px-4 py-3 rounded-lg transition-colors ${activeTab === 'users' ? 'bg-blue-50 text-[#1e3a5f]' : 'text-gray-700 hover:bg-gray-50'}`}>
                      <UserIcon className="h-5 w-5 mr-3" />
                      <span className="font-medium">Users & Permissions</span>
                    </button>
                    <button onClick={() => setActiveTab('notifications')} className={`w-full flex items-center px-4 py-3 rounded-lg transition-colors ${activeTab === 'notifications' ? 'bg-blue-50 text-[#1e3a5f]' : 'text-gray-700 hover:bg-gray-50'}`}>
                      <BellIcon className="h-5 w-5 mr-3" />
                      <span className="font-medium">Notifications</span>
                    </button>
                    <button onClick={() => setActiveTab('security')} className={`w-full flex items-center px-4 py-3 rounded-lg transition-colors ${activeTab === 'security' ? 'bg-blue-50 text-[#1e3a5f]' : 'text-gray-700 hover:bg-gray-50'}`}>
                      <LockIcon className="h-5 w-5 mr-3" />
                      <span className="font-medium">Security</span>
                    </button>
                    <button onClick={() => setActiveTab('billing')} className={`w-full flex items-center px-4 py-3 rounded-lg transition-colors ${activeTab === 'billing' ? 'bg-blue-50 text-[#1e3a5f]' : 'text-gray-700 hover:bg-gray-50'}`}>
                      <CreditCardIcon className="h-5 w-5 mr-3" />
                      <span className="font-medium">Billing</span>
                    </button>
                    <button onClick={() => setActiveTab('appearance')} className={`w-full flex items-center px-4 py-3 rounded-lg transition-colors ${activeTab === 'appearance' ? 'bg-blue-50 text-[#1e3a5f]' : 'text-gray-700 hover:bg-gray-50'}`}>
                      <PaletteIcon className="h-5 w-5 mr-3" />
                      <span className="font-medium">Appearance</span>
                    </button>
                    <button onClick={() => setActiveTab('emails')} className={`w-full flex items-center px-4 py-3 rounded-lg transition-colors ${activeTab === 'emails' ? 'bg-blue-50 text-[#1e3a5f]' : 'text-gray-700 hover:bg-gray-50'}`}>
                      <MailIcon className="h-5 w-5 mr-3" />
                      <span className="font-medium">Email Templates</span>
                    </button>
                    <button onClick={() => setActiveTab('integrations')} className={`w-full flex items-center px-4 py-3 rounded-lg transition-colors ${activeTab === 'integrations' ? 'bg-blue-50 text-[#1e3a5f]' : 'text-gray-700 hover:bg-gray-50'}`}>
                      <ServerIcon className="h-5 w-5 mr-3" />
                      <span className="font-medium">Integrations</span>
                    </button>
                  </nav>
                </div>
              </div>
              {/* Settings content */}
              <div className="flex-1">
                <div className="bg-white rounded-xl shadow-sm p-6">
                  {activeTab === 'general' && <motion.div initial={{
                  opacity: 0,
                  y: 10
                }} animate={{
                  opacity: 1,
                  y: 0
                }} transition={{
                  duration: 0.3
                }}>
                      <h2 className="text-xl font-semibold text-gray-900 mb-6">
                        Business Information
                      </h2>
                      <form className="space-y-6">
                        <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                          <div>
                            <label htmlFor="businessName" className="block text-sm font-medium text-gray-700 mb-1">
                              Business Name
                            </label>
                            <input type="text" id="businessName" name="businessName" value={formData.businessName} onChange={handleChange} className="w-full px-4 py-2 rounded-lg border border-gray-300 focus:ring-2 focus:ring-blue-500 focus:border-transparent" />
                          </div>
                          <div>
                            <label htmlFor="email" className="block text-sm font-medium text-gray-700 mb-1">
                              Email Address
                            </label>
                            <input type="email" id="email" name="email" value={formData.email} onChange={handleChange} className="w-full px-4 py-2 rounded-lg border border-gray-300 focus:ring-2 focus:ring-blue-500 focus:border-transparent" />
                          </div>
                          <div>
                            <label htmlFor="phone" className="block text-sm font-medium text-gray-700 mb-1">
                              Phone Number
                            </label>
                            <input type="text" id="phone" name="phone" value={formData.phone} onChange={handleChange} className="w-full px-4 py-2 rounded-lg border border-gray-300 focus:ring-2 focus:ring-blue-500 focus:border-transparent" />
                          </div>
                          <div>
                            <label htmlFor="website" className="block text-sm font-medium text-gray-700 mb-1">
                              Website
                            </label>
                            <input type="text" id="website" name="website" value={formData.website} onChange={handleChange} className="w-full px-4 py-2 rounded-lg border border-gray-300 focus:ring-2 focus:ring-blue-500 focus:border-transparent" />
                          </div>
                          <div>
                            <label htmlFor="address" className="block text-sm font-medium text-gray-700 mb-1">
                              Street Address
                            </label>
                            <input type="text" id="address" name="address" value={formData.address} onChange={handleChange} className="w-full px-4 py-2 rounded-lg border border-gray-300 focus:ring-2 focus:ring-blue-500 focus:border-transparent" />
                          </div>
                          <div className="grid grid-cols-3 gap-4">
                            <div>
                              <label htmlFor="city" className="block text-sm font-medium text-gray-700 mb-1">
                                City
                              </label>
                              <input type="text" id="city" name="city" value={formData.city} onChange={handleChange} className="w-full px-4 py-2 rounded-lg border border-gray-300 focus:ring-2 focus:ring-blue-500 focus:border-transparent" />
                            </div>
                            <div>
                              <label htmlFor="state" className="block text-sm font-medium text-gray-700 mb-1">
                                State
                              </label>
                              <input type="text" id="state" name="state" value={formData.state} onChange={handleChange} className="w-full px-4 py-2 rounded-lg border border-gray-300 focus:ring-2 focus:ring-blue-500 focus:border-transparent" />
                            </div>
                            <div>
                              <label htmlFor="zipCode" className="block text-sm font-medium text-gray-700 mb-1">
                                ZIP Code
                              </label>
                              <input type="text" id="zipCode" name="zipCode" value={formData.zipCode} onChange={handleChange} className="w-full px-4 py-2 rounded-lg border border-gray-300 focus:ring-2 focus:ring-blue-500 focus:border-transparent" />
                            </div>
                          </div>
                        </div>
                        <div className="pt-6 border-t border-gray-200">
                          <h3 className="text-lg font-medium text-gray-900 mb-4">
                            Business Preferences
                          </h3>
                          <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                            <div>
                              <label htmlFor="taxRate" className="block text-sm font-medium text-gray-700 mb-1">
                                Default Tax Rate (%)
                              </label>
                              <input type="text" id="taxRate" name="taxRate" value={formData.taxRate} onChange={handleChange} className="w-full px-4 py-2 rounded-lg border border-gray-300 focus:ring-2 focus:ring-blue-500 focus:border-transparent" />
                            </div>
                            <div>
                              <label htmlFor="currency" className="block text-sm font-medium text-gray-700 mb-1">
                                Currency
                              </label>
                              <select id="currency" name="currency" value={formData.currency} onChange={handleChange} className="w-full px-4 py-2 rounded-lg border border-gray-300 focus:ring-2 focus:ring-blue-500 focus:border-transparent">
                                <option value="USD">USD - US Dollar</option>
                                <option value="EUR">EUR - Euro</option>
                                <option value="GBP">GBP - British Pound</option>
                                <option value="CAD">
                                  CAD - Canadian Dollar
                                </option>
                                <option value="AUD">
                                  AUD - Australian Dollar
                                </option>
                              </select>
                            </div>
                            <div>
                              <label htmlFor="dateFormat" className="block text-sm font-medium text-gray-700 mb-1">
                                Date Format
                              </label>
                              <select id="dateFormat" name="dateFormat" value={formData.dateFormat} onChange={handleChange} className="w-full px-4 py-2 rounded-lg border border-gray-300 focus:ring-2 focus:ring-blue-500 focus:border-transparent">
                                <option value="MM/DD/YYYY">MM/DD/YYYY</option>
                                <option value="DD/MM/YYYY">DD/MM/YYYY</option>
                                <option value="YYYY-MM-DD">YYYY-MM-DD</option>
                              </select>
                            </div>
                            <div>
                              <label htmlFor="timeZone" className="block text-sm font-medium text-gray-700 mb-1">
                                Time Zone
                              </label>
                              <select id="timeZone" name="timeZone" value={formData.timeZone} onChange={handleChange} className="w-full px-4 py-2 rounded-lg border border-gray-300 focus:ring-2 focus:ring-blue-500 focus:border-transparent">
                                <option value="America/Los_Angeles">
                                  Pacific Time (US & Canada)
                                </option>
                                <option value="America/Denver">
                                  Mountain Time (US & Canada)
                                </option>
                                <option value="America/Chicago">
                                  Central Time (US & Canada)
                                </option>
                                <option value="America/New_York">
                                  Eastern Time (US & Canada)
                                </option>
                                <option value="UTC">UTC</option>
                              </select>
                            </div>
                          </div>
                        </div>
                        <div className="pt-6 border-t border-gray-200">
                          <h3 className="text-lg font-medium text-gray-900 mb-4">
                            Business Logo
                          </h3>
                          <div className="flex items-center space-x-6">
                            <div className="w-24 h-24 rounded-lg bg-gray-200 flex items-center justify-center">
                              <span className="text-gray-400">Logo</span>
                            </div>
                            <div>
                              <button type="button" className="px-4 py-2 bg-gray-100 hover:bg-gray-200 text-gray-800 rounded-lg transition-colors">
                                Upload New Logo
                              </button>
                              <p className="text-xs text-gray-500 mt-2">
                                Recommended size: 200x200px. Max file size: 2MB.
                              </p>
                            </div>
                          </div>
                        </div>
                      </form>
                    </motion.div>}
                  {activeTab === 'notifications' && <motion.div initial={{
                  opacity: 0,
                  y: 10
                }} animate={{
                  opacity: 1,
                  y: 0
                }} transition={{
                  duration: 0.3
                }}>
                      <h2 className="text-xl font-semibold text-gray-900 mb-6">
                        Notification Settings
                      </h2>
                      <form className="space-y-6">
                        <div className="space-y-4">
                          <div className="flex items-center justify-between py-3 border-b border-gray-200">
                            <div>
                              <h4 className="text-base font-medium text-gray-900">
                                Enable Notifications
                              </h4>
                              <p className="text-sm text-gray-500">
                                Master toggle for all system notifications
                              </p>
                            </div>
                            <div className="flex items-center">
                              <label className="relative inline-flex items-center cursor-pointer">
                                <input type="checkbox" name="enableNotifications" checked={formData.enableNotifications} onChange={handleChange} className="sr-only peer" />
                                <div className="w-11 h-6 bg-gray-200 peer-focus:outline-none peer-focus:ring-4 peer-focus:ring-blue-300 rounded-full peer peer-checked:after:translate-x-full peer-checked:after:border-white after:content-[''] after:absolute after:top-[2px] after:left-[2px] after:bg-white after:border-gray-300 after:border after:rounded-full after:h-5 after:w-5 after:transition-all peer-checked:bg-blue-600"></div>
                              </label>
                            </div>
                          </div>
                          <div className="flex items-center justify-between py-3 border-b border-gray-200">
                            <div>
                              <h4 className="text-base font-medium text-gray-900">
                                Email Notifications
                              </h4>
                              <p className="text-sm text-gray-500">
                                Receive notifications via email
                              </p>
                            </div>
                            <div className="flex items-center">
                              <label className="relative inline-flex items-center cursor-pointer">
                                <input type="checkbox" name="emailNotifications" checked={formData.emailNotifications} onChange={handleChange} disabled={!formData.enableNotifications} className="sr-only peer" />
                                <div className={`w-11 h-6 ${!formData.enableNotifications ? 'bg-gray-100' : 'bg-gray-200'} peer-focus:outline-none peer-focus:ring-4 peer-focus:ring-blue-300 rounded-full peer peer-checked:after:translate-x-full peer-checked:after:border-white after:content-[''] after:absolute after:top-[2px] after:left-[2px] after:bg-white after:border-gray-300 after:border after:rounded-full after:h-5 after:w-5 after:transition-all peer-checked:bg-blue-600`}></div>
                              </label>
                            </div>
                          </div>
                          <div className="flex items-center justify-between py-3 border-b border-gray-200">
                            <div>
                              <h4 className="text-base font-medium text-gray-900">
                                SMS Notifications
                              </h4>
                              <p className="text-sm text-gray-500">
                                Receive notifications via SMS
                              </p>
                            </div>
                            <div className="flex items-center">
                              <label className="relative inline-flex items-center cursor-pointer">
                                <input type="checkbox" name="smsNotifications" checked={formData.smsNotifications} onChange={handleChange} disabled={!formData.enableNotifications} className="sr-only peer" />
                                <div className={`w-11 h-6 ${!formData.enableNotifications ? 'bg-gray-100' : 'bg-gray-200'} peer-focus:outline-none peer-focus:ring-4 peer-focus:ring-blue-300 rounded-full peer peer-checked:after:translate-x-full peer-checked:after:border-white after:content-[''] after:absolute after:top-[2px] after:left-[2px] after:bg-white after:border-gray-300 after:border after:rounded-full after:h-5 after:w-5 after:transition-all peer-checked:bg-blue-600`}></div>
                              </label>
                            </div>
                          </div>
                        </div>
                        <div className="pt-4">
                          <h3 className="text-lg font-medium text-gray-900 mb-4">
                            Notification Events
                          </h3>
                          <div className="space-y-4">
                            {['New Service Request', 'Appointment Reminder', 'Service Completed', 'New Vehicle Added', 'Low Inventory Alert'].map((event, index) => <div key={index} className="flex items-center justify-between py-3 border-b border-gray-200">
                                <div>
                                  <h4 className="text-base font-medium text-gray-900">
                                    {event}
                                  </h4>
                                </div>
                                <div className="flex items-center space-x-4">
                                  <label className="flex items-center">
                                    <input type="checkbox" checked={true} disabled={!formData.enableNotifications || !formData.emailNotifications} className="rounded border-gray-300 text-blue-600 focus:ring-blue-500 h-4 w-4" />
                                    <span className="ml-2 text-sm text-gray-600">
                                      Email
                                    </span>
                                  </label>
                                  <label className="flex items-center">
                                    <input type="checkbox" checked={index < 2} disabled={!formData.enableNotifications || !formData.smsNotifications} className="rounded border-gray-300 text-blue-600 focus:ring-blue-500 h-4 w-4" />
                                    <span className="ml-2 text-sm text-gray-600">
                                      SMS
                                    </span>
                                  </label>
                                </div>
                              </div>)}
                          </div>
                        </div>
                      </form>
                    </motion.div>}
                  {(activeTab === 'users' || activeTab === 'security' || activeTab === 'billing' || activeTab === 'appearance' || activeTab === 'emails' || activeTab === 'integrations') && <motion.div initial={{
                  opacity: 0,
                  y: 10
                }} animate={{
                  opacity: 1,
                  y: 0
                }} transition={{
                  duration: 0.3
                }} className="text-center py-8">
                      <div className="mx-auto w-16 h-16 bg-gray-100 rounded-full flex items-center justify-center mb-4">
                        {activeTab === 'users' && <UserIcon className="h-8 w-8 text-gray-400" />}
                        {activeTab === 'security' && <LockIcon className="h-8 w-8 text-gray-400" />}
                        {activeTab === 'billing' && <CreditCardIcon className="h-8 w-8 text-gray-400" />}
                        {activeTab === 'appearance' && <PaletteIcon className="h-8 w-8 text-gray-400" />}
                        {activeTab === 'emails' && <MailIcon className="h-8 w-8 text-gray-400" />}
                        {activeTab === 'integrations' && <ServerIcon className="h-8 w-8 text-gray-400" />}
                      </div>
                      <h3 className="text-lg font-medium text-gray-900 mb-2">
                        {activeTab === 'users' && 'User Management'}
                        {activeTab === 'security' && 'Security Settings'}
                        {activeTab === 'billing' && 'Billing & Subscription'}
                        {activeTab === 'appearance' && 'Appearance Settings'}
                        {activeTab === 'emails' && 'Email Templates'}
                        {activeTab === 'integrations' && 'System Integrations'}
                      </h3>
                      <p className="text-gray-500 max-w-md mx-auto">
                        This section is currently under development. Check back
                        soon for updates.
                      </p>
                      <button className="mt-6 px-4 py-2 bg-gray-100 hover:bg-gray-200 text-gray-800 rounded-lg transition-colors">
                        Learn More
                      </button>
                    </motion.div>}
                </div>
              </div>
            </div>
          </div>
        </main>
      </div>
    </div>;
};
export default Settings;