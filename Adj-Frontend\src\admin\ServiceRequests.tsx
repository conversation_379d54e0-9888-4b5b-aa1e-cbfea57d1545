import React, { useState, useEffect } from 'react';
import { useSearchParams } from 'react-router-dom';
import { SearchIcon, EyeIcon, CheckIcon, XIcon, ClockIcon, ArrowUpDownIcon, FilterIcon, ChevronLeftIcon, ChevronRightIcon, PhoneIcon, MailIcon, MessageSquareIcon, CalendarIcon, FileTextIcon, MoreHorizontalIcon, ArrowLeftIcon, MenuIcon } from 'lucide-react';
import AdminSidebar from '../components/AdminSidebar';

// Move getStatusColor function outside components so both can access it
const getStatusColor = (status: string) => {
  switch (status.toLowerCase()) {
    case 'completed':
      return 'bg-green-100 text-green-800';
    case 'in progress':
      return 'bg-blue-100 text-blue-800';
    case 'scheduled':
      return 'bg-purple-100 text-purple-800';
    case 'pending':
      return 'bg-yellow-100 text-yellow-800';
    case 'cancelled':
      return 'bg-gray-100 text-gray-800';
    default:
      return 'bg-gray-100 text-gray-800';
  }
};

const AdminServiceRequests = () => {
  const [searchParams] = useSearchParams();
  const [sidebarOpen, setSidebarOpen] = useState(false);
  const [searchQuery, setSearchQuery] = useState('');
  const [statusFilter, setStatusFilter] = useState('all');
  const [currentPage, setCurrentPage] = useState(1);
  const [sortField, setSortField] = useState('requestDate');
  const [sortDirection, setSortDirection] = useState('desc');
  const [selectedRequest, setSelectedRequest] = useState(null);

  // Handle URL parameters for pre-filtering
  useEffect(() => {
    const status = searchParams.get('status');
    const view = searchParams.get('view');
    
    if (status) {
      setStatusFilter(status);
    }
    
    if (view === 'schedule') {
      // For schedule view, sort by scheduled date to show appointments chronologically
      setSortField('scheduledDate');
      setSortDirection('asc');
    }
  }, [searchParams]);

  const toggleSidebar = () => {
    setSidebarOpen(!sidebarOpen);
  };
  // Sample service requests data
  const serviceRequests = [{
    id: 'SR-2023-089',
    customer: {
      name: 'Michael Rodriguez',
      email: '<EMAIL>',
      phone: '(*************'
    },
    service: 'Transmission Rebuilding',
    vehicle: '2018 Toyota Camry',
    status: 'In Progress',
    requestDate: '2023-10-12',
    scheduledDate: '2023-10-15',
    description: 'Transmission slipping in 2nd and 3rd gear. Vehicle has approximately 85,000 miles.',
    notes: 'Customer approved estimate. Parts ordered on Oct 13.',
    estimatedCost: '$3,800',
    assignedTo: 'David Chen'
  }, {
    id: 'SR-2023-088',
    customer: {
      name: 'Sarah Johnson',
      email: '<EMAIL>',
      phone: '(*************'
    },
    service: 'Engine Diagnostics',
    vehicle: '2020 Ford F-150',
    status: 'Completed',
    requestDate: '2023-10-11',
    scheduledDate: '2023-10-11',
    completionDate: '2023-10-11',
    description: 'Check engine light on. Loss of power when accelerating.',
    notes: 'Diagnosed faulty O2 sensor and replaced. Test drive confirms issue resolved.',
    estimatedCost: '$350',
    finalCost: '$365',
    assignedTo: 'Adam Johnson'
  }, {
    id: 'SR-2023-087',
    customer: {
      name: 'David Chen',
      email: '<EMAIL>',
      phone: '(*************'
    },
    service: 'Brake Service',
    vehicle: '2019 Mercedes C300',
    status: 'Scheduled',
    requestDate: '2023-10-10',
    scheduledDate: '2023-10-15',
    description: 'Squeaking noise when braking. Pedal feels soft.',
    estimatedCost: '$650',
    assignedTo: 'Sarah Williams'
  }, {
    id: 'SR-2023-086',
    customer: {
      name: 'Emily Wilson',
      email: '<EMAIL>',
      phone: '(*************'
    },
    service: 'Key Programming',
    vehicle: '2017 Lexus RX350',
    status: 'Pending',
    requestDate: '2023-10-09',
    description: 'Need replacement key fob programmed. Customer will provide the new fob.'
  }, {
    id: 'SR-2023-085',
    customer: {
      name: 'Robert Garcia',
      email: '<EMAIL>',
      phone: '(*************'
    },
    service: 'Oil Change & Inspection',
    vehicle: '2021 Chevrolet Tahoe',
    status: 'Completed',
    requestDate: '2023-10-08',
    scheduledDate: '2023-10-09',
    completionDate: '2023-10-09',
    description: 'Regular maintenance oil change and multi-point inspection.',
    notes: 'Recommended tire rotation at next service.',
    estimatedCost: '$95',
    finalCost: '$95',
    assignedTo: 'Michael Chen'
  }, {
    id: 'SR-2023-084',
    customer: {
      name: 'Jennifer Lee',
      email: '<EMAIL>',
      phone: '(*************'
    },
    service: 'Suspension Repair',
    vehicle: '2016 Honda Accord',
    status: 'Cancelled',
    requestDate: '2023-10-07',
    description: 'Clunking noise from front end when going over bumps.',
    notes: 'Customer cancelled - decided to go to dealer.',
    estimatedCost: '$850'
  }];
  // Filter requests based on search query and status
  const filteredRequests = serviceRequests.filter(request => {
    const matchesSearch = request.customer.name.toLowerCase().includes(searchQuery.toLowerCase()) || request.id.toLowerCase().includes(searchQuery.toLowerCase()) || request.vehicle.toLowerCase().includes(searchQuery.toLowerCase()) || request.service.toLowerCase().includes(searchQuery.toLowerCase());
    const matchesStatus = statusFilter === 'all' || request.status.toLowerCase() === statusFilter.toLowerCase();
    return matchesSearch && matchesStatus;
  });
  // Sort requests
  const sortedRequests = [...filteredRequests].sort((a, b) => {
    if (sortField === 'requestDate') {
      return sortDirection === 'asc' 
        ? new Date(a.requestDate).getTime() - new Date(b.requestDate).getTime() 
        : new Date(b.requestDate).getTime() - new Date(a.requestDate).getTime();
    }
    if (sortField === 'scheduledDate') {
      // Handle null scheduled dates
      if (!a.scheduledDate) return sortDirection === 'asc' ? 1 : -1;
      if (!b.scheduledDate) return sortDirection === 'asc' ? -1 : 1;
      return sortDirection === 'asc' 
        ? new Date(a.scheduledDate).getTime() - new Date(b.scheduledDate).getTime() 
        : new Date(b.scheduledDate).getTime() - new Date(a.scheduledDate).getTime();
    }
    if (sortField === 'customer') {
      return sortDirection === 'asc' ? a.customer.name.localeCompare(b.customer.name) : b.customer.name.localeCompare(a.customer.name);
    }
    return 0;
  });
  // Pagination
  const itemsPerPage = 10;
  const totalPages = Math.ceil(sortedRequests.length / itemsPerPage);
  const paginatedRequests = sortedRequests.slice((currentPage - 1) * itemsPerPage, currentPage * itemsPerPage);
  const handleSort = (field: string) => {
    if (sortField === field) {
      setSortDirection(sortDirection === 'asc' ? 'desc' : 'asc');
    } else {
      setSortField(field);
      setSortDirection('asc');
    }
  };
  const handleViewRequest = (request: any) => {
    setSelectedRequest(request);
  };
  if (selectedRequest) {
    return <ServiceRequestDetail request={selectedRequest} onBack={() => setSelectedRequest(null)} />;
  }
  return <div className="flex h-screen bg-gray-100">
      {/* Sidebar for desktop */}
      <aside className="bg-[#1e3a5f] text-white w-64 flex-shrink-0 hidden md:block">
        <AdminSidebar activePage="service-requests" />
      </aside>

      {/* Mobile sidebar */}
      <div className={`fixed inset-0 bg-gray-800 bg-opacity-75 z-40 md:hidden transition-opacity duration-200 ${sidebarOpen ? 'opacity-100' : 'opacity-0 pointer-events-none'}`} onClick={toggleSidebar}></div>

      <aside className={`fixed top-0 left-0 h-full bg-[#1e3a5f] text-white w-64 z-50 transform transition-transform duration-200 ease-in-out md:hidden ${sidebarOpen ? 'translate-x-0' : '-translate-x-full'}`}>
        <div className="absolute top-4 right-4">
          <button className="text-white p-2" onClick={toggleSidebar}>
            <XIcon className="h-6 w-6" />
          </button>
        </div>
        <AdminSidebar activePage="service-requests" />
      </aside>

      {/* Main content */}
      <div className="flex-1 flex flex-col overflow-hidden">
        {/* Top header */}
        <header className="bg-white shadow-sm z-10">
          <div className="px-4 py-4 flex justify-between items-center">
            <div className="flex items-center md:hidden">
              <button className="text-gray-600 p-2" onClick={toggleSidebar}>
                <MenuIcon className="h-6 w-6" />
              </button>
            </div>
            <h1 className="text-xl font-semibold text-gray-800">
              Service Requests
            </h1>
            <div></div>
          </div>
        </header>

        {/* Main content area */}
        <main className="flex-1 overflow-y-auto bg-gray-100 p-4">
          <div className="bg-white rounded-xl shadow-md p-4 mb-6">
            <div className="flex flex-col md:flex-row md:items-center justify-between gap-4">
              <div className="flex-1">
                <div className="relative">
                  <input type="text" placeholder="Search by customer, ID, vehicle..." value={searchQuery} onChange={e => setSearchQuery(e.target.value)} className="w-full pl-10 pr-4 py-2 rounded-lg border border-gray-300 focus:ring-2 focus:ring-blue-500 focus:border-transparent" />
                  <SearchIcon className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 h-5 w-5" />
                </div>
              </div>
              <div className="flex items-center space-x-4">
                <div className="flex items-center">
                  <span className="text-gray-600 mr-2">Status:</span>
                  <select value={statusFilter} onChange={e => setStatusFilter(e.target.value)} className="px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent">
                    <option value="all">All</option>
                    <option value="pending">Pending</option>
                    <option value="scheduled">Scheduled</option>
                    <option value="in progress">In Progress</option>
                    <option value="completed">Completed</option>
                    <option value="cancelled">Cancelled</option>
                  </select>
                </div>
                <button className="p-2 text-gray-600 hover:text-gray-800">
                  <FilterIcon className="h-5 w-5" />
                </button>
              </div>
            </div>
          </div>

          {/* Schedule View Indicator */}
          {searchParams.get('view') === 'schedule' && (
            <div className="bg-blue-50 border-l-4 border-blue-500 p-4 mb-6 rounded-r-lg">
              <div className="flex items-center">
                <CalendarIcon className="h-5 w-5 text-blue-600 mr-2" />
                <div>
                  <p className="text-sm font-medium text-blue-800">
                    Schedule View Active
                  </p>
                  <p className="text-xs text-blue-600 mt-1">
                    Showing {statusFilter === 'all' ? 'all' : statusFilter} appointments sorted by scheduled date
                  </p>
                </div>
              </div>
            </div>
          )}

          <div className="bg-white rounded-xl shadow-md overflow-hidden">
            <div className="overflow-x-auto">
              <table className="min-w-full">
                <thead>
                  <tr className="bg-gray-100 border-b border-gray-200">
                    <th className="px-4 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                      ID
                    </th>
                    <th className="px-4 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                      <button onClick={() => handleSort('customer')} className="flex items-center">
                        Customer
                        <ArrowUpDownIcon className="h-4 w-4 ml-1" />
                      </button>
                    </th>
                    <th className="px-4 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                      Service
                    </th>
                    <th className="px-4 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                      Vehicle
                    </th>
                    <th className="px-4 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                      Status
                    </th>
                    <th className="px-4 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                      <button onClick={() => handleSort('requestDate')} className="flex items-center">
                        Request Date
                        <ArrowUpDownIcon className="h-4 w-4 ml-1" />
                      </button>
                    </th>
                    <th className="px-4 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                      <button onClick={() => handleSort('scheduledDate')} className="flex items-center">
                        Scheduled Date
                        <ArrowUpDownIcon className="h-4 w-4 ml-1" />
                      </button>
                    </th>
                    <th className="px-4 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider">
                      Actions
                    </th>
                  </tr>
                </thead>
                <tbody className="divide-y divide-gray-200">
                  {paginatedRequests.map(request => <tr key={request.id} className="hover:bg-gray-50">
                      <td className="px-4 py-4 whitespace-nowrap text-sm font-medium text-blue-600">
                        {request.id}
                      </td>
                      <td className="px-4 py-4 whitespace-nowrap">
                        <div className="text-sm font-medium text-gray-900">
                          {request.customer.name}
                        </div>
                        <div className="text-sm text-gray-500">
                          {request.customer.phone}
                        </div>
                      </td>
                      <td className="px-4 py-4 whitespace-nowrap text-sm text-gray-900">
                        {request.service}
                      </td>
                      <td className="px-4 py-4 whitespace-nowrap text-sm text-gray-900">
                        {request.vehicle}
                      </td>
                      <td className="px-4 py-4 whitespace-nowrap">
                        <span className={`px-2 py-1 inline-flex text-xs leading-5 font-semibold rounded-full ${getStatusColor(request.status)}`}>
                          {request.status}
                        </span>
                      </td>
                      <td className="px-4 py-4 whitespace-nowrap text-sm text-gray-500">
                        {request.requestDate}
                      </td>
                      <td className="px-4 py-4 whitespace-nowrap text-sm text-gray-500">
                        {request.scheduledDate || '-'}
                      </td>
                      <td className="px-4 py-4 whitespace-nowrap text-right text-sm font-medium">
                        <button onClick={() => handleViewRequest(request)} className="text-blue-600 hover:text-blue-900">
                          <EyeIcon className="h-5 w-5" />
                        </button>
                      </td>
                    </tr>)}
                </tbody>
              </table>
            </div>

            {/* Pagination */}
            {totalPages > 1 && <div className="px-4 py-3 flex items-center justify-between border-t border-gray-200">
                <div className="flex-1 flex justify-between items-center">
                  <p className="text-sm text-gray-700">
                    Showing{' '}
                    <span className="font-medium">
                      {(currentPage - 1) * itemsPerPage + 1}
                    </span>{' '}
                    to{' '}
                    <span className="font-medium">
                      {Math.min(currentPage * itemsPerPage, sortedRequests.length)}
                    </span>{' '}
                    of{' '}
                    <span className="font-medium">{sortedRequests.length}</span>{' '}
                    results
                  </p>
                  <div className="flex space-x-2">
                    <button onClick={() => setCurrentPage(currentPage - 1)} disabled={currentPage === 1} className={`relative inline-flex items-center px-4 py-2 border border-gray-300 text-sm font-medium rounded-md bg-white ${currentPage === 1 ? 'text-gray-300' : 'text-gray-700 hover:bg-gray-50'}`}>
                      <ChevronLeftIcon className="h-4 w-4" />
                    </button>
                    <button onClick={() => setCurrentPage(currentPage + 1)} disabled={currentPage === totalPages} className={`relative inline-flex items-center px-4 py-2 border border-gray-300 text-sm font-medium rounded-md bg-white ${currentPage === totalPages ? 'text-gray-300' : 'text-gray-700 hover:bg-gray-50'}`}>
                      <ChevronRightIcon className="h-4 w-4" />
                    </button>
                  </div>
                </div>
              </div>}
          </div>
        </main>
      </div>
    </div>;
};
const ServiceRequestDetail = ({
  request,
  onBack
}: {
  request: any;
  onBack: () => void;
}) => {
  const [status, setStatus] = useState(request.status);
  const [notes, setNotes] = useState(request.notes || '');
  const [estimatedCost, setEstimatedCost] = useState(request.estimatedCost || '');
  const [finalCost, setFinalCost] = useState(request.finalCost || '');
  const [assignedTo, setAssignedTo] = useState(request.assignedTo || '');
  const [scheduledDate, setScheduledDate] = useState(request.scheduledDate || '');
  const statusOptions = ['Pending', 'Scheduled', 'In Progress', 'Completed', 'Cancelled'];
  const technicians = ['Adam Johnson', 'Sarah Williams', 'David Chen', 'Michael Chen', 'Jennifer Lee'];
  const handleUpdateRequest = (e: React.FormEvent) => {
    e.preventDefault();
    // In a real app, this would send data to your backend
    console.log('Request updated:', {
      id: request.id,
      status,
      notes,
      estimatedCost,
      finalCost,
      assignedTo,
      scheduledDate
    });
    onBack();
  };
  return <div className="flex h-screen bg-gray-100">
      {/* Sidebar for desktop */}
      <aside className="bg-[#1e3a5f] text-white w-64 flex-shrink-0 hidden md:block">
        <AdminSidebar activePage="service-requests" />
      </aside>

      {/* Main content */}
      <div className="flex-1 flex flex-col overflow-hidden">
        <main className="flex-1 overflow-y-auto bg-gray-100 p-6">
          <button onClick={onBack} className="flex items-center text-blue-600 hover:text-blue-800 mb-6">
            <ArrowLeftIcon className="h-4 w-4 mr-2" />
            Back to Service Requests
          </button>

          <div className="bg-white rounded-xl shadow-md overflow-hidden">
            <div className="px-6 py-4 border-b border-gray-200 flex justify-between items-center">
              <div>
                <h2 className="text-xl font-bold text-gray-900">
                  Service Request: {request.id}
                </h2>
                <p className="text-gray-600">
                  {request.service} for {request.vehicle}
                </p>
              </div>
              <span className={`px-3 py-1 inline-flex text-sm leading-5 font-semibold rounded-full ${getStatusColor(request.status)}`}>
                {request.status}
              </span>
            </div>

            <div className="p-6">
              <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
                {/* Customer Information */}
                <div className="bg-gray-50 rounded-lg p-4">
                  <h3 className="text-lg font-semibold text-gray-900 mb-4">
                    Customer Information
                  </h3>
                  <div className="space-y-3">
                    <div>
                      <p className="text-gray-500 text-sm">Name</p>
                      <p className="font-medium">{request.customer.name}</p>
                    </div>
                    <div className="flex items-center">
                      <PhoneIcon className="h-4 w-4 text-gray-400 mr-2" />
                      <a href={`tel:${request.customer.phone}`} className="text-blue-600 hover:text-blue-800">
                        {request.customer.phone}
                      </a>
                    </div>
                    <div className="flex items-center">
                      <MailIcon className="h-4 w-4 text-gray-400 mr-2" />
                      <a href={`mailto:${request.customer.email}`} className="text-blue-600 hover:text-blue-800">
                        {request.customer.email}
                      </a>
                    </div>
                  </div>
                  <div className="mt-4 pt-4 border-t border-gray-200 flex space-x-2">
                    <button className="p-2 text-blue-600 hover:text-blue-800 rounded-full hover:bg-blue-50">
                      <PhoneIcon className="h-5 w-5" />
                    </button>
                    <button className="p-2 text-blue-600 hover:text-blue-800 rounded-full hover:bg-blue-50">
                      <MailIcon className="h-5 w-5" />
                    </button>
                    <button className="p-2 text-blue-600 hover:text-blue-800 rounded-full hover:bg-blue-50">
                      <MessageSquareIcon className="h-5 w-5" />
                    </button>
                  </div>
                </div>

                {/* Request Details */}
                <div className="md:col-span-2 bg-gray-50 rounded-lg p-4">
                  <h3 className="text-lg font-semibold text-gray-900 mb-4">
                    Request Details
                  </h3>
                  <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                    <div>
                      <p className="text-gray-500 text-sm">Service Type</p>
                      <p className="font-medium">{request.service}</p>
                    </div>
                    <div>
                      <p className="text-gray-500 text-sm">Vehicle</p>
                      <p className="font-medium">{request.vehicle}</p>
                    </div>
                    <div>
                      <p className="text-gray-500 text-sm">Request Date</p>
                      <div className="flex items-center">
                        <CalendarIcon className="h-4 w-4 text-gray-400 mr-2" />
                        <p>{request.requestDate}</p>
                      </div>
                    </div>
                    <div>
                      <p className="text-gray-500 text-sm">Scheduled Date</p>
                      <div className="flex items-center">
                        <ClockIcon className="h-4 w-4 text-gray-400 mr-2" />
                        <p>{request.scheduledDate || 'Not scheduled'}</p>
                      </div>
                    </div>
                    {request.completionDate && <div>
                        <p className="text-gray-500 text-sm">Completion Date</p>
                        <div className="flex items-center">
                          <CheckIcon className="h-4 w-4 text-green-500 mr-2" />
                          <p>{request.completionDate}</p>
                        </div>
                      </div>}
                    {request.assignedTo && <div>
                        <p className="text-gray-500 text-sm">Assigned To</p>
                        <p className="font-medium">{request.assignedTo}</p>
                      </div>}
                  </div>
                  <div className="mt-4 pt-4 border-t border-gray-200">
                    <p className="text-gray-500 text-sm mb-2">Description</p>
                    <p className="bg-white p-3 rounded border border-gray-200">
                      {request.description}
                    </p>
                  </div>
                  {request.notes && <div className="mt-4 pt-4 border-t border-gray-200">
                      <p className="text-gray-500 text-sm mb-2">Notes</p>
                      <p className="bg-white p-3 rounded border border-gray-200">
                        {request.notes}
                      </p>
                    </div>}
                  <div className="mt-4 pt-4 border-t border-gray-200 flex flex-wrap gap-4">
                    {request.estimatedCost && <div>
                        <p className="text-gray-500 text-sm">Estimated Cost</p>
                        <p className="font-semibold text-lg">
                          {request.estimatedCost}
                        </p>
                      </div>}
                    {request.finalCost && <div>
                        <p className="text-gray-500 text-sm">Final Cost</p>
                        <p className="font-semibold text-lg text-blue-700">
                          {request.finalCost}
                        </p>
                      </div>}
                  </div>
                </div>

                {/* Update Form */}
                <div className="md:col-span-3">
                  <h3 className="text-lg font-semibold text-gray-900 mb-4">
                    Update Service Request
                  </h3>
                  <form onSubmit={handleUpdateRequest}>
                    <div className="grid grid-cols-1 md:grid-cols-3 gap-6 mb-6">
                      <div>
                        <label htmlFor="status" className="block text-sm font-medium text-gray-700 mb-1">
                          Status
                        </label>
                        <select id="status" value={status} onChange={e => setStatus(e.target.value)} className="w-full px-4 py-2 rounded-lg border border-gray-300 focus:ring-2 focus:ring-blue-500 focus:border-transparent">
                          {statusOptions.map(option => <option key={option} value={option}>
                              {option}
                            </option>)}
                        </select>
                      </div>
                      <div>
                        <label htmlFor="assignedTo" className="block text-sm font-medium text-gray-700 mb-1">
                          Assigned To
                        </label>
                        <select id="assignedTo" value={assignedTo} onChange={e => setAssignedTo(e.target.value)} className="w-full px-4 py-2 rounded-lg border border-gray-300 focus:ring-2 focus:ring-blue-500 focus:border-transparent">
                          <option value="">Select Technician</option>
                          {technicians.map(tech => <option key={tech} value={tech}>
                              {tech}
                            </option>)}
                        </select>
                      </div>
                      <div>
                        <label htmlFor="scheduledDate" className="block text-sm font-medium text-gray-700 mb-1">
                          Scheduled Date
                        </label>
                        <input type="date" id="scheduledDate" value={scheduledDate} onChange={e => setScheduledDate(e.target.value)} className="w-full px-4 py-2 rounded-lg border border-gray-300 focus:ring-2 focus:ring-blue-500 focus:border-transparent" />
                      </div>
                      <div>
                        <label htmlFor="estimatedCost" className="block text-sm font-medium text-gray-700 mb-1">
                          Estimated Cost
                        </label>
                        <input type="text" id="estimatedCost" value={estimatedCost} onChange={e => setEstimatedCost(e.target.value)} placeholder="$0.00" className="w-full px-4 py-2 rounded-lg border border-gray-300 focus:ring-2 focus:ring-blue-500 focus:border-transparent" />
                      </div>
                      <div>
                        <label htmlFor="finalCost" className="block text-sm font-medium text-gray-700 mb-1">
                          Final Cost
                        </label>
                        <input type="text" id="finalCost" value={finalCost} onChange={e => setFinalCost(e.target.value)} placeholder="$0.00" className="w-full px-4 py-2 rounded-lg border border-gray-300 focus:ring-2 focus:ring-blue-500 focus:border-transparent" />
                      </div>
                    </div>
                    <div className="mb-6">
                      <label htmlFor="notes" className="block text-sm font-medium text-gray-700 mb-1">
                        Notes
                      </label>
                      <textarea id="notes" value={notes} onChange={e => setNotes(e.target.value)} rows={4} className="w-full px-4 py-2 rounded-lg border border-gray-300 focus:ring-2 focus:ring-blue-500 focus:border-transparent" placeholder="Add notes about the service request..."></textarea>
                    </div>
                    <div className="flex justify-between">
                      <div className="flex space-x-2">
                        <button type="button" className="px-4 py-2 border border-gray-300 rounded-lg text-gray-700 hover:bg-gray-50 flex items-center">
                          <FileTextIcon className="h-5 w-5 mr-2" />
                          Generate Invoice
                        </button>
                        <button type="button" className="px-4 py-2 border border-gray-300 rounded-lg text-gray-700 hover:bg-gray-50 flex items-center">
                          <MessageSquareIcon className="h-5 w-5 mr-2" />
                          Message Customer
                        </button>
                        <div className="relative">
                          <button type="button" className="px-4 py-2 border border-gray-300 rounded-lg text-gray-700 hover:bg-gray-50">
                            <MoreHorizontalIcon className="h-5 w-5" />
                          </button>
                        </div>
                      </div>
                      <div>
                        <button type="submit" className="px-6 py-2 bg-[#1e3a5f] hover:bg-blue-800 text-white rounded-lg transition-colors">
                          Update Request
                        </button>
                      </div>
                    </div>
                  </form>
                </div>
              </div>
            </div>
          </div>
        </main>
      </div>
    </div>;
};
export default AdminServiceRequests;